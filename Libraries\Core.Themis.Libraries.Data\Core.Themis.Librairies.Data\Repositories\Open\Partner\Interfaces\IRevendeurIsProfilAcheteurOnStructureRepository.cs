using Core.Themis.Libraries.Data.Entities.Open.Partner;
using Core.Themis.Libraries.Data.Repositories.Common.Interfaces;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Core.Themis.Libraries.Data.Repositories.Open.Partner.Interfaces
{
    public interface IRevendeurIsProfilAcheteurOnStructureRepository : IGenericStructureRepository<RevendeurIsProfilAcheteurOnStructureEntity>
    {
        /// <summary>
        /// Obtient toutes les liaisons pour un revendeur donné
        /// </summary>
        /// <param name="structureId">ID de la structure</param>
        /// <param name="revendeurId">ID du revendeur</param>
        /// <returns>Liste des liaisons</returns>
        Task<IEnumerable<RevendeurIsProfilAcheteurOnStructureEntity>> GetLiaisonsByRevendeurAsync(int structureId, int revendeurId);

        /// <summary>
        /// Obtient toutes les liaisons pour une structure donnée
        /// </summary>
        /// <param name="structureId">ID de la structure</param>
        /// <returns>Liste des liaisons</returns>
        Task<IEnumerable<RevendeurIsProfilAcheteurOnStructureEntity>> GetLiaisonsByStructureAsync(int structureId);

        /// <summary>
        /// Obtient toutes les liaisons pour un profil acheteur donné
        /// </summary>
        /// <param name="structureId">ID de la structure</param>
        /// <param name="profilAcheteurId">ID du profil acheteur</param>
        /// <returns>Liste des liaisons</returns>
        Task<IEnumerable<RevendeurIsProfilAcheteurOnStructureEntity>> GetLiaisonsByProfilAcheteurAsync(int structureId, int profilAcheteurId);

        /// <summary>
        /// Vérifie si une liaison existe déjà
        /// </summary>
        /// <param name="structureId">ID de la structure</param>
        /// <param name="revendeurId">ID du revendeur</param>
        /// <param name="profilAcheteurId">ID du profil acheteur</param>
        /// <returns>True si la liaison existe</returns>
        Task<bool> LiaisonExistsAsync(int structureId, int revendeurId, int profilAcheteurId);

    }
}
