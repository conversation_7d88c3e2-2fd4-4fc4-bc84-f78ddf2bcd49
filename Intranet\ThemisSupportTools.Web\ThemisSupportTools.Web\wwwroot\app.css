html, body {
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

a, .btn-link {
    color: #006bb7;
}

.btn-primary {
    color: #fff;
    background-color: #1b6ec2;
    border-color: #1861ac;
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

.content {
    padding-top: 1.1rem;
}

h1:focus {
    outline: none;
}

.valid.modified:not([type=checkbox]) {
    outline: 1px solid #26b050;
}

.invalid {
    outline: 1px solid #e50000;
}

.validation-message {
    color: #e50000;
}

.blazor-error-boundary {
    background: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNDkiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIG92ZXJmbG93PSJoaWRkZW4iPjxkZWZzPjxjbGlwUGF0aCBpZD0iY2xpcDAiPjxyZWN0IHg9IjIzNSIgeT0iNTEiIHdpZHRoPSI1NiIgaGVpZ2h0PSI0OSIvPjwvY2xpcFBhdGg+PC9kZWZzPjxnIGNsaXAtcGF0aD0idXJsKCNjbGlwMCkiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0yMzUgLTUxKSI+PHBhdGggZD0iTTI2My41MDYgNTFDMjY0LjcxNyA1MSAyNjUuODEzIDUxLjQ4MzcgMjY2LjYwNiA1Mi4yNjU4TDI2Ny4wNTIgNTIuNzk4NyAyNjcuNTM5IDUzLjYyODMgMjkwLjE4NSA5Mi4xODMxIDI5MC41NDUgOTIuNzk1IDI5MC42NTYgOTIuOTk2QzI5MC44NzcgOTMuNTEzIDI5MSA5NC4wODE1IDI5MSA5NC42NzgyIDI5MSA5Ny4wNjUxIDI4OS4wMzggOTkgMjg2LjYxNyA5OUwyNDAuMzgzIDk5QzIzNy45NjMgOTkgMjM2IDk3LjA2NTEgMjM2IDk0LjY3ODIgMjM2IDk0LjM3OTkgMjM2LjAzMSA5NC4wODg2IDIzNi4wODkgOTMuODA3MkwyMzYuMzM4IDkzLjAxNjIgMjM2Ljg1OCA5Mi4xMzE0IDI1OS40NzMgNTMuNjI5NCAyNTkuOTYxIDUyLjc5ODUgMjYwLjQwNyA1Mi4yNjU4QzI2MS4yIDUxLjQ4MzcgMjYyLjI5NiA1MSAyNjMuNTA2IDUxWk0yNjMuNTg2IDY2LjAxODNDMjYwLjczNyA2Ni4wMTgzIDI1OS4zMTMgNjcuMTI0NSAyNTkuMzEzIDY5LjMzNyAyNTkuMzEzIDY5LjYxMDIgMjU5LjMzMiA2OS44NjA4IDI1OS4zNzEgNzAuMDg4N0wyNjEuNzk1IDg0LjAxNjEgMjY1LjM4IDg0LjAxNjEgMjY3LjgyMSA2OS43NDc1QzI2Ny44NiA2OS43MzA5IDI2Ny44NzkgNjkuNTg3NyAyNjcuODc5IDY5LjMxNzkgMjY3Ljg3OSA2Ny4xMTgyIDI2Ni40NDggNjYuMDE4MyAyNjMuNTg2IDY2LjAxODNaTTI2My41NzYgODYuMDU0N0MyNjEuMDQ5IDg2LjA1NDcgMjU5Ljc4NiA4Ny4zMDA1IDI1OS43ODYgODkuNzkyMSAyNTkuNzg2IDkyLjI4MzcgMjYxLjA0OSA5My41Mjk1IDI2My41NzYgOTMuNTI5NSAyNjYuMTE2IDkzLjUyOTUgMjY3LjM4NyA5Mi4yODM3IDI2Ny4zODcgODkuNzkyMSAyNjcuMzg3IDg3LjMwMDUgMjY2LjExNiA4Ni4wNTQ3IDI2My41NzYgODYuMDU0N1oiIGZpbGw9IiNGRkU1MDAiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPjwvZz48L3N2Zz4=) no-repeat 1rem/1.8rem, #b32121;
    padding: 1rem 1rem 1rem 3.7rem;
    color: white;
}

    .blazor-error-boundary::after {
        content: "An error has occurred."
    }

.darker-border-checkbox.form-check-input {
    border-color: #929292;
}

/* ===== PARTNERS PAGE STYLES ===== */

/* Partner item container */
.partner-item {
    transition: all 0.2s ease;
    border-left: 4px solid transparent;
}

.partner-item:hover {
    border-left-color: #667eea;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transform: translateX(2px);
}

/* Structures section styling */
.structures-section {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 0.75rem;
    border: 1px solid #e9ecef;
}

/* Partner actions (edit/delete buttons) */
.partner-actions {
    opacity: 0.8;
    transition: opacity 0.2s ease;
}

.partner-item:hover .partner-actions {
    opacity: 1;
}

/* Partner name styling */
.partner-name {
    color: #2c3e50;
    font-weight: 600;
}

/* Role badge styling */
.role-badge {
    font-size: 0.75rem;
}

/* Structures label */
.structures-label {
    color: #6c757d;
    font-weight: 500;
    font-size: 0.875rem;
}

/* ===== STRUCTURE PARTNERS COMPACT COMPONENT STYLES ===== */

/* Inline structure display */
.structure-inline {
    display: inline-block;
    margin: 0.125rem;
    padding: 0.25rem 0.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
    position: relative;
    cursor: pointer;
    transition: all 0.2s ease;
}

.structure-inline:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

/* Structure ID styling */
.structure-inline .structure-id {
    font-weight: 600;
    margin-right: 0.25rem;
}

/* Partners inline display */
.partners-inline {
    display: inline-block;
    margin-left: 0.25rem;
    font-size: 0.7rem;
    opacity: 0.9;
}

/* Partner count badge */
.partner-count {
    background: rgba(255,255,255,0.2);
    padding: 0.125rem 0.25rem;
    border-radius: 8px;
    margin-left: 0.25rem;
    font-size: 0.65rem;
}

/* Container for inline structures */
.structures-container-inline {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    align-items: center;
}

/* More structures indicator */
.more-structures {
    font-size: 0.7rem;
    color: #6c757d;
    font-style: italic;
}

/* ===== LISTE STRUCTURE WITH PARTENAIRES PAGE STYLES ===== */

/* Structure card styling */
.structure-card {
    border: none;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.structure-card:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

/* Structure header */
.structure-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem;
    border-radius: 8px 8px 0 0;
}

.structure-id {
    background: rgba(255,255,255,0.2);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.85rem;
}

.structure-name {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
}

/* Partners container */
.partners-container {
    padding: 1.25rem;
    background: #f8f9fa;
}

.partner-badge {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    margin: 0.2rem;
    display: inline-block;
    box-shadow: 0 2px 4px rgba(40,167,69,0.3);
    transition: all 0.2s ease;
}

.partner-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(40,167,69,0.4);
}

.no-partners {
    color: #6c757d;
    font-style: italic;
    padding: 0.5rem;
    text-align: center;
    background: #e9ecef;
    border-radius: 6px;
}

/* Stats card */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    text-align: center;
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

/* Page header */
.page-header {
    background: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-bottom: 1px solid #e9ecef;
}

/* Search box */
.search-box {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    transition: all 0.3s ease;
    width: 100%;
    max-width: 400px;
}

.search-box:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    outline: none;
}

/* ===== PAGINATION STYLES ===== */

.pagination .page-link {
    color: #667eea;
    border-color: #dee2e6;
    padding: 0.5rem 0.75rem;
    font-weight: 500;
    transition: all 0.2s ease;
    border-radius: 6px;
    margin: 0 2px;
}

.pagination .page-link:hover {
    color: #495057;
    background-color: #f8f9fa;
    border-color: #dee2e6;
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background-color: #667eea;
    border-color: #667eea;
    color: white;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
    background-color: #fff;
    border-color: #dee2e6;
    cursor: not-allowed;
}

.pagination .page-link:focus {
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Boutons de navigation */
.btn-outline-primary {
    border-color: #667eea;
    color: #667eea;
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.2s ease;
}

.btn-outline-primary:hover {
    background-color: #667eea;
    border-color: #667eea;
    color: white;
    transform: translateY(-1px);
}

.btn-outline-primary.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Styles pour l'interface de liaison revendeur-acheteur */
.liaison-revendeur-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.page-header {
    margin-bottom: 30px;
    text-align: center;
}

.page-header h3 {
    color: #1b6ec2;
    margin-bottom: 10px;
}

.page-description {
    color: #6c757d;
    font-size: 1.1rem;
}

.loading-container {
    text-align: center;
    padding: 40px;
}

.loading-container .spinner-border {
    width: 3rem;
    height: 3rem;
    margin-bottom: 20px;
}

.liaison-steps-container {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.step-container {
    background: white;
    border-radius: 8px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 1px 5px rgba(0,0,0,0.1);
}

.step-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.step-header h4 {
    color: #1b6ec2;
    margin: 0;
}

.step-header p {
    margin: 5px 0;
    color: #6c757d;
}

.revendeurs-list, .profils-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.revendeur-item, .profil-item {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.revendeur-item:hover, .profil-item:hover {
    background: #e9ecef;
    border-color: #1b6ec2;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.revendeur-info, .profil-info {
    display: flex;
    flex-direction: column;
}

.revendeur-info strong, .profil-info strong {
    color: #495057;
    font-size: 1.1rem;
    margin-bottom: 5px;
}

.revendeur-id, .profil-id {
    color: #6c757d;
    font-size: 0.9rem;
}

.structure-selection {
    max-width: 500px;
    margin: 0 auto;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    display: block;
}

.form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    padding: 10px 15px;
    font-size: 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
    border-color: #1b6ec2;
    box-shadow: 0 0 0 0.2rem rgba(27, 110, 194, 0.25);
}

.action-buttons {
    text-align: center;
    margin-top: 25px;
}

.action-buttons .btn {
    margin: 0 10px;
    padding: 10px 25px;
    font-weight: 600;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.confirmation-container {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.confirmation-container .alert {
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
}

.confirmation-container ul {
    text-align: left;
    display: inline-block;
    margin: 15px 0;
}

.confirmation-container li {
    margin-bottom: 8px;
    color: #495057;
}

.no-data {
    text-align: center;
    padding: 40px;
    color: #6c757d;
}

.no-data p {
    font-size: 1.1rem;
    margin-bottom: 20px;
}

.alert-dismissible .btn-close {
    padding: 0.5rem 0.75rem;
}

/* Responsive design */
@media (max-width: 768px) {
    .liaison-revendeur-container {
        padding: 10px;
    }

    .liaison-steps-container {
        padding: 15px;
    }

    .step-container {
        padding: 15px;
    }

    .step-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .revendeurs-list, .profils-list {
        grid-template-columns: 1fr;
    }

    .revendeur-item, .profil-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .action-buttons .btn {
        display: block;
        width: 100%;
        margin: 5px 0;
    }
}

/* Styles pour les éléments sélectionnés */
.profil-item.selected {
    background: #e3f2fd;
    border-color: #1b6ec2;
    box-shadow: 0 2px 8px rgba(27, 110, 194, 0.3);
}

.profil-item input[type="radio"] {
    margin-right: 10px;
    transform: scale(1.2);
}

.profils-selection {
    margin-top: 20px;
}

.profils-structure-list {
    margin-top: 20px;
}

.structure-selector {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.liaison-summary {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    margin: 10px 0;
}

.liaison-summary p {
    margin-bottom: 8px;
}

.liaison-summary .text-info {
    font-style: italic;
    color: #17a2b8 !important;
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #dee2e6;
}

