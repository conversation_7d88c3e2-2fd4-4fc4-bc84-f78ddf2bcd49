using AutoMapper;
using Core.Themis.Libraries.Data.Context.Interfaces;
using Core.Themis.Libraries.Data.Entities.Open.Revendeur;
using Core.Themis.Libraries.Data.Repositories.Common;
using Core.Themis.Libraries.Data.Repositories.Open.Revendeur.Interfaces;
using Core.Themis.Libraries.Utilities.Enums;
using Dapper;
using Microsoft.Extensions.Caching.Memory;
using System.Data.SqlClient;

namespace Core.Themis.Libraries.Data.Repositories.Open.Revendeur
{
    public class RevendeurRepository : GenericStructureRepository<RevendeurEntity>, IRevendeurRepository
    {
        private readonly IDbContext _dbContext;

        public RevendeurRepository(IDbContext dbContext, IMemoryCache memoryCache, IMapper mapper)
            : base(ContextType.Open, dbContext, memoryCache, mapper)
        {
            _dbContext = dbContext;
        }

        /// <summary>
        /// Récupère tous les revendeurs de la table revendeurs
        /// </summary>
        /// <param name="structureId">ID de la structure pour la connexion</param>
        /// <returns>Liste des revendeurs</returns>
        public IEnumerable<RevendeurEntity> GetAllRevendeurs(int structureId)
        {
            try
            {
                using SqlConnection connection = _dbContext.GetStructureConnection($"{structureId:0000}");
                connection.Open();

                string sqlQuery = @"SELECT * FROM revendeurs ORDER BY name";

                return connection.Query<RevendeurEntity>(sqlQuery);
            }
            catch (System.Data.SqlClient.SqlException)
            {
                // Retourner une liste vide si la connexion à la base de données échoue
                return new List<RevendeurEntity>();
            }
        }
    }
}
