using Core.Themis.Libraries.Data.Entities.Open.BuyerProfil;
using Core.Themis.Libraries.Data.Repositories.Common.Interfaces;
using Core.Themis.Libraries.DTO;
using System.Collections.Generic;

namespace Core.Themis.Libraries.Data.Repositories.Open.BuyerProfil.Interfaces
{
    public interface IBuyerProfilRepository : IGenericStructureRepository<ProfilAcheteurEntity>
    {
        BuyerProfilDTO Get(int structureId, int buyerProfilId, string login = null, string password = null);


        /// <summary>
        /// SELECT * FROM profil_acheteur
        /// </summary>
        /// <param name="structureId"></param>
        /// <returns></returns>
        IEnumerable<ProfilAcheteurEntity> GetBuyerProfils(int structureId);


        /// <summary>
        /// select * from mode_paiement_of_profil_acheteur
        /// </summary>
        /// <param name="structureId"></param>
        /// <returns></returns>
        IEnumerable<ModePaiementOfProfilAcheteur> GetPaymentMethodOfBuyerProfils(int structureId);

        /// <summary>
        /// Récupère tous les revendeurs (is_revendeur = 1) d'une structure spécifique
        /// </summary>
        /// <param name="structureId"></param>
        /// <returns></returns>
        IEnumerable<ProfilAcheteurEntity> GetRevendeurs(int structureId);


    }
}
