﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="about" xml:space="preserve">
    <value>À propos</value>
  </data>
  <data name="dropdown_filter_groupKey_fieldName" xml:space="preserve">
    <value>Key Group Filter</value>
  </data>
  <data name="search_by_key_fieldName" xml:space="preserve">
    <value>Search by keys</value>
  </data>
  <data name="loading_spinner_span" xml:space="preserve">
    <value>Loading...</value>
  </data>
  <data name="show_mandatory_fields_label" xml:space="preserve">
    <value> Show required fields</value>
  </data>
  <data name="show_modified_fields_label" xml:space="preserve">
    <value>Show modified fields</value>
  </data>
  <data name="restore_last_file_button_btnSecondary" xml:space="preserve">
    <value> Restore the last file</value>
  </data>
  <data name="change_structure_button_btnDanger" xml:space="preserve">
    <value>Changer de structure</value>
  </data>
  <data name="save_button" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="confirm_switch_to_new_structure" xml:space="preserve">
    <value>Are you sure you want to change the structure?</value>
  </data>
  <data name="changeStructure_button_yes" xml:space="preserve">
    <value>CHANGER DE STRUCTURE</value>
  </data>
  <data name="cancel_button_no" xml:space="preserve">
    <value>ANNULER</value>
  </data>
  <data name="restore_action" xml:space="preserve">
    <value>Restore</value>
  </data>
  <data name="label_create_section" xml:space="preserve">
    <value>Create a section</value>
  </data>
  <data name="confirm_restore_last_saved_file" xml:space="preserve">
    <value>Are you sure you want to restore the last saved file?</value>
  </data>
  <data name="search_key_input_placeholder" xml:space="preserve">
    <value>Enter a key...</value>
  </data>
  <data name="dropdown_choose_section" xml:space="preserve">
    <value>Choose a Section...</value>
  </data>
  <data name="choose_structure" xml:space="preserve">
    <value> Choose a structure</value>
  </data>
  <data name="confirm_dialog_yes_button_text" xml:space="preserve">
    <value>I understand</value>
  </data>
  <data name="confirm_dialog_message_time_expired" xml:space="preserve">
    <value>The time has expired. We will redirect you to the homepage</value>
  </data>
  <data name="toast_message_title_modifications_in_progress" xml:space="preserve">
    <value>Changes in Progress</value>
  </data>
  <data name="toast_message_no_changes_in_form" xml:space="preserve">
    <value>No changes in this form!</value>
  </data>
  <data name="toast_message_title_form_validation" xml:space="preserve">
    <value>Form Validation</value>
  </data>
  <data name="toast_message_empty_required_fields" xml:space="preserve">
    <value>There are still empty required fields</value>
  </data>
  <data name="confirm_save_changes_message" xml:space="preserve">
    <value>Are you sure you want to save the changes?</value>
  </data>
  <data name="popup_save_success_message" xml:space="preserve">
    <value>Save modifications successfully, Well Done!</value>
  </data>
  <data name="popup_save_success_button" xml:space="preserve">
    <value>Back to Home</value>
  </data>
  <data name="file_in_use_message" xml:space="preserve">
    <value>The file is currently in use by {0} on Structure No. {1} ({2}), please change the structure !</value>
  </data>
  <data name="create-new-structure" xml:space="preserve">
    <value>Create a new structure</value>
  </data>
  <data name="config-ini" xml:space="preserve">
    <value>Manage config.ini file</value>
  </data>
  <data name="appsettings-plateforms" xml:space="preserve">
    <value>AppSettings Platforms</value>
  </data>
  <data name="partners" xml:space="preserve">
    <value>Partner Management</value>
  </data>
  <data name="translations-terms" xml:space="preserve">
    <value>Translation Management</value>
  </data>
  <data name="button_edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="button_delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="select_all_sections" xml:space="preserve">
    <value>All sections</value>
  </data>
  <data name="select_view_empty_fields_language" xml:space="preserve">
    <value>View empty fields of the language</value>
  </data>
  <data name="button_filter" xml:space="preserve">
    <value>Filtrer</value>
  </data>
  <data name="icon_search" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="button_new_translation" xml:space="preserve">
    <value>New Translation</value>
  </data>
  <data name="label_translation_count" xml:space="preserve">
    <value>{0} translation(s)</value>
  </data>
  <data name="dialog_delete_message" xml:space="preserve">
    <value>Are you sure you want to delete the translation {0}?</value>
  </data>
  <data name="button_cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="translations_title" xml:space="preserve">
    <value>Translations</value>
  </data>
  <data name="new_partner" xml:space="preserve">
    <value>New Partner</value>
  </data>
  <data name="search_button" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="confirm_delete_partner" xml:space="preserve">
    <value>Are you sure you want to delete this partner &lt;strong&gt;{0}&lt;/strong&gt;?</value>
  </data>
  <data name="confirm_button" xml:space="preserve">
    <value>Confirm</value>
  </data>
  <data name="choose_partner" xml:space="preserve">
    <value>Choose a partner</value>
  </data>
  <data name="new_secret_key" xml:space="preserve">
    <value>New Secret Key</value>
  </data>
  <data name="button_validate" xml:space="preserve">
    <value>Validate</value>
  </data>
  <data name="title_parameters" xml:space="preserve">
    <value>Parameters</value>
  </data>
  <data name="label_name_partener" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="partener_secret_key" xml:space="preserve">
    <value>Secret key</value>
  </data>
  <data name="label_partener_structure" xml:space="preserve">
    <value>Structures</value>
  </data>
  <data name="label_partener_roles" xml:space="preserve">
    <value>Roles</value>
  </data>
  <data name="title_edit_section" xml:space="preserve">
    <value>Edit a section</value>
  </data>
  <data name="label_key_section" xml:space="preserve">
    <value>Section key</value>
  </data>
  <data name="label_description_section" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="h1_sections" xml:space="preserve">
    <value>Sections</value>
  </data>
  <data name="search_by_keyword" xml:space="preserve">
    <value>Search by keyword</value>
  </data>
  <data name="new_section" xml:space="preserve">
    <value>New section</value>
  </data>
  <data name="confirmation_delete_section" xml:space="preserve">
    <value>Are you sure you want to delete the section</value>
  </data>
  <data name="new_variable" xml:space="preserve">
    <value>New variable</value>
  </data>
  <data name="label_variable_name" xml:space="preserve">
    <value>Variable name</value>
  </data>
  <data name="label_description_variable" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="self_closing_tag" xml:space="preserve">
    <value>Self-closing tag</value>
  </data>
  <data name="edit_variable" xml:space="preserve">
    <value>Edit a variable</value>
  </data>
  <data name="h1_variables" xml:space="preserve">
    <value>Variables</value>
  </data>
  <data name="confirm_delete_variable" xml:space="preserve">
    <value>Are you sure you want to delete this variable?</value>
  </data>
  <data name="remaining_time" xml:space="preserve">
    <value>Remaining time</value>
  </data>
  <data name="new_translation" xml:space="preserve">
    <value>New translation</value>
  </data>
  <data name="section_name" xml:space="preserve">
    <value>Section name</value>
  </data>
  <data name="select_section" xml:space="preserve">
    <value>Select a section</value>
  </data>
  <data name="translation_key" xml:space="preserve">
    <value>Translation key</value>
  </data>
  <data name="parent_translation_key" xml:space="preserve">
    <value>Parent translation key</value>
  </data>
  <data name="select_parent_key" xml:space="preserve">
    <value>Select a parent key</value>
  </data>
  <data name="linked_to_existing_key" xml:space="preserve">
    <value>Linked to an existing key</value>
  </data>
  <data name="visible_only_for_admin_status" xml:space="preserve">
    <value>Visible only for Admin status</value>
  </data>
  <data name="edit_translation" xml:space="preserve">
    <value>Edit a translation</value>
  </data>
  <data name="toast_message_title_deletion_success" xml:space="preserve">
    <value>Deletion Successful</value>
  </data>
  <data name="toast_message_deletion_successful" xml:space="preserve">
    <value>The deletion was successfully completed.</value>
  </data>
  <data name="error_partner_exists" xml:space="preserve">
    <value>The partner '{0}' already exists.</value>
  </data>
  <data name="select_language" xml:space="preserve">
    <value>Select a language</value>
  </data>
  <data name="label_translation_indiv_platform" xml:space="preserve">
    <value>Manage translation INDIV platform</value>
  </data>
  <data name="label_translation_customer_platform" xml:space="preserve">
    <value>Manage translations from the CUSTOMER platform</value>
  </data>
  <data name="label_translation_abo_platform" xml:space="preserve">
    <value>Manage translations from the ABO platform</value>
  </data>
  <data name="button_translations_indiv_platform" xml:space="preserve">
    <value>Translations of the INDIV platform</value>
    <comment>Translations of the INDIV platform</comment>
  </data>
  <data name="button_translations_customer_platform" xml:space="preserve">
    <value>Customer Area Translations</value>
  </data>
  <data name="button_translations_abo_platform" xml:space="preserve">
    <value>Subscription Platform Translations</value>
  </data>
  <data name="error_exist_partener_title" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="delete_error_title" xml:space="preserve">
    <value>Deletion Error</value>
  </data>
  <data name="delete_errorm_message" xml:space="preserve">
    <value>An error occurred while deleting the translation key {0}.</value>
  </data>
  <data name="delete_success_title" xml:space="preserve">
    <value>Deletion Successful</value>
  </data>
  <data name="delete_success_message" xml:space="preserve">
    <value>The translation key {0} has been successfully deleted.</value>
  </data>
  <data name="partners_count" xml:space="preserve">
    <value>partner(s)</value>
  </data>
  <data name="no_partners_associated" xml:space="preserve">
    <value>No associated partners</value>
  </data>
  <data name="choose_structure" xml:space="preserve">
    <value>Choose a Structure</value>
  </data>
  <data name="select_structure" xml:space="preserve">
    <value>Select Structure</value>
  </data>
  <data name="select_structure_placeholder" xml:space="preserve">
    <value>-- Select a structure --</value>
  </data>
  <data name="search_partners_in_structure" xml:space="preserve">
    <value>Search partners in this structure</value>
  </data>
  <data name="search_partner_placeholder" xml:space="preserve">
    <value>Enter partner name...</value>
  </data>
  <data name="partners_of_structure" xml:space="preserve">
    <value>Partners of</value>
  </data>
  <data name="partners_found" xml:space="preserve">
    <value>partner(s) found</value>
  </data>
  <data name="linked_structures" xml:space="preserve">
    <value>linked structure(s)</value>
  </data>
  <data name="no_partners_found_search" xml:space="preserve">
    <value>No partners found matching your search</value>
  </data>
  <data name="no_partners_in_structure" xml:space="preserve">
    <value>This structure has no associated partners</value>
  </data>
  <data name="select_structure_to_view_partners" xml:space="preserve">
    <value>Select a structure above to view its partners</value>
  </data>
  <data name="toast_save_success_message" xml:space="preserve">
    <value>Registration successful</value>
  </data>
  <data name="toast_message_title_success_edit" xml:space="preserve">
    <value>Modification Successful</value>
  </data>
  <data name="toast_message_success_edit_partner_saved" xml:space="preserve">
    <value>The partner's modifications have been successfully saved.</value>
  </data>
  <data name="message_champ_obligatoire_xml" xml:space="preserve">
    <value>This field is required</value>
  </data>
  <data name="variables_comment_montant_gratuit_xml" xml:space="preserve">
    <value>Three cases for the amount value: 0:0, 1: free, 2: not included</value>
  </data>
  <data name="variables_comment_xml" xml:space="preserve">
    <value>Values ranging from 5 to 7</value>
  </data>
  <data name="variables_comment_type_alpha_xml" xml:space="preserve">
    <value>Alphanumeric</value>
  </data>
  <data name="paiement_comment_operateur_creation_commande_xml" xml:space="preserve">
    <value>Operator used for creating the order</value>
  </data>
  <data name="paiement_comment_web_operateur_id_xml" xml:space="preserve">
    <value>Web operator ID</value>
  </data>
  <data name="paiement_comment_web_post_id_xml" xml:space="preserve">
    <value>Web post ID</value>
  </data>
  <data name="paiement_comment_currency_xml" xml:space="preserve">
    <value>Currency used in the transaction</value>
  </data>
  <data name="paiement_comment_activation_paiement_multiple_xml" xml:space="preserve">
    <value>Enable multiple payments</value>
  </data>
  <data name="paiement_comment_capture_day_xml" xml:space="preserve">
    <value>Payment capture day</value>
  </data>
  <data name="paiement_comment_periode_xml" xml:space="preserve">
    <value>Payment period</value>
  </data>
  <data name="paiement_comment_montant_min_nb_payment_xml" xml:space="preserve">
    <value>Minimum amount for number of payments</value>
  </data>
  <data name="paiement_comment_filieren_payment_xml" xml:space="preserve">
    <value>Payment channel</value>
  </data>
  <data name="paiement_comment_url_retour_err_xml" xml:space="preserve">
    <value>Return URL in case of error</value>
  </data>
  <data name="paiement_comment_url_retour_vente_xml" xml:space="preserve">
    <value>Return URL after a sale</value>
  </data>
  <data name="paiement_comment_url_retour_err_vente_xml" xml:space="preserve">
    <value>Return URL in case of error during the sale</value>
  </data>
  <data name="paiement_comment_url_retourannul_vente_xml" xml:space="preserve">
    <value>Return URL for a canceled sal</value>
  </data>
  <data name="paiement_comment_url_retour_groupe_xml" xml:space="preserve">
    <value>Return URL for a group</value>
  </data>
  <data name="paiement_comment_url_retour_err_groupe_xml" xml:space="preserve">
    <value>Return URL in case of error for a group</value>
  </data>
  <data name="paiement_comment_url_retourannul_groupe_xml" xml:space="preserve">
    <value>Return URL for a canceled group</value>
  </data>
  <data name="paiement_comment_nb_payment_xml" xml:space="preserve">
    <value>Number of payments allowed</value>
  </data>
  <data name="paiement_comment_doe_edition_xml" xml:space="preserve">
    <value>Enable document edition</value>
  </data>
  <data name="paiement_comment_activation_asynchrone_xml" xml:space="preserve">
    <value>Enable asynchronous processing</value>
  </data>
  <data name="paiement_comment_not_asynchrone_pa_xml" xml:space="preserve">
    <value>Not asynchronous for buyer profile</value>
  </data>
  <data name="paiement_comment_urls_site_xml" xml:space="preserve">
    <value>URL of the payment site</value>
  </data>
  <data name="paiement_comment_version_xml" xml:space="preserve">
    <value>Payment application version</value>
  </data>
  <data name="paiement_comment_activation_template_mail_xml" xml:space="preserve">
    <value>Email template activation</value>
  </data>
  <data name="paiement_comment_desactiver_envoi_piece_jointe_pdf_xml" xml:space="preserve">
    <value>Disable attachment sending (PDF)</value>
  </data>
  <data name="paiement_comment_id_marchand_spplus_xml" xml:space="preserve">
    <value>SP+ Merchant ID</value>
  </data>
  <data name="paiement_comment_url_retour_abo_xml" xml:space="preserve">
    <value>Return URL for subscription</value>
  </data>
  <data name="paiement_comment_url_retour_err_abo_xml" xml:space="preserve">
    <value>Return URL in case of error for subscription</value>
  </data>
  <data name="paiement_comment_url_retourannul_abo_xml" xml:space="preserve">
    <value>Return URL for subscription cancellation</value>
  </data>
  <data name="param_langue_com_configini_xml" xml:space="preserve">
    <value>Language parameter for configuration</value>
  </data>
  <data name="param_comment_devise_code_xml" xml:space="preserve">
    <value>Currency code</value>
  </data>
  <data name="param_comment_devise_iso_xml" xml:space="preserve">
    <value>ISO code of the currency</value>
  </data>
  <data name="param_comment_devise_before_xml" xml:space="preserve">
    <value>Position of the currency (before or after the amount)</value>
  </data>
  <data name="param_comment_devise_separator_xml" xml:space="preserve">
    <value>Currency separator</value>
  </data>
  <data name="param_comment_prestataire_paiement_xml" xml:space="preserve">
    <value>Payment provider</value>
  </data>
  <data name="param_comment_bonscadeaux_xml" xml:space="preserve">
    <value>Gift vouchers available</value>
  </data>
  <data name="param_comment_bannername_xml" xml:space="preserve">
    <value>Banner name</value>
  </data>
  <data name="param_comment_extensionbanner_xml" xml:space="preserve">
    <value>Banner extension (take the first one found)</value>
  </data>
  <data name="param_comment_insurance_xml" xml:space="preserve">
    <value>Manage insurance contract, yes=1</value>
  </data>
  <data name="param_comment_minutestogotopaiement_xml" xml:space="preserve">
    <value>Time before payment (in minutes)</value>
  </data>
  <data name="param_comment_fraisdossiermontantgratuit_xml" xml:space="preserve">
    <value>File fees for free amount</value>
  </data>
  <data name="param_comment_groupe_manif_exclude_widget_xml" xml:space="preserve">
    <value>Manifestation group excluded from widgets</value>
  </data>
  <data name="param_comment_multilangue_xml" xml:space="preserve">
    <value>Multi-language activation</value>
  </data>
  <data name="param_comment_langue_en_xml" xml:space="preserve">
    <value>English language</value>
  </data>
  <data name="param_comment_langue_it_xml" xml:space="preserve">
    <value>Italian language</value>
  </data>
  <data name="param_comment_langue_sp_xml" xml:space="preserve">
    <value>Spanish language</value>
  </data>
  <data name="param_comment_langue_de_xml" xml:space="preserve">
    <value>German language</value>
  </data>
  <data name="param_comment_showonlyonedate_xml" xml:space="preserve">
    <value>Display only one date</value>
  </data>
  <data name="param_comment_usecustomerarea_xml" xml:space="preserve">
    <value>Use customer area</value>
  </data>
  <data name="facture_mode_comment_facturemode_id_xml" xml:space="preserve">
    <value>Invoice payment method</value>
  </data>
  <data name="param_comment_facturemode_seuildeblocage_xml" xml:space="preserve">
    <value>Blocking threshold</value>
  </data>
  <data name="comment_smtpclient_ip_xml" xml:space="preserve">
    <value>Mail server IP</value>
  </data>
  <data name="comment_navigation_accesfchoixseance_xml " xml:space="preserve">
    <value>Obsolete key group</value>
  </data>
  <data name="comment_navigation_panier_xml" xml:space="preserve">
    <value>Obsolete key group</value>
  </data>
  <data name="comment_priseplacessurplan_showunavailableseats_xml" xml:space="preserve">
    <value>Show unavailable seats</value>
  </data>
  <data name="comment_priseplacessurplan_startimagesfauteuilsx_xml" xml:space="preserve">
    <value>Minimum chair X dimension</value>
  </data>
  <data name="comment_priseplacessurplan_startimagesfauteuilsy_xml" xml:space="preserve">
    <value>Minimum chair Y dimension</value>
  </data>
  <data name="comment_marker_printathome_xml" xml:space="preserve">
    <value>Display a specific text if print@home is selected</value>
  </data>
  <data name="comment_marker_controlprice_xml" xml:space="preserve">
    <value>ID of prices that display a specific text</value>
  </data>
  <data name="comment_email_senderadresse_xml" xml:space="preserve">
    <value>Sender address for purchase confirmation email</value>
  </data>
  <data name="comment_email_replyadresse_xml" xml:space="preserve">
    <value>Reply-to address</value>
  </data>
  <data name="comment_email_copyadresse_xml" xml:space="preserve">
    <value>Copy address for purchase confirmation email</value>
  </data>
  <data name="comment_email_blindcopyadresse_xml" xml:space="preserve">
    <value>Blind copy address</value>
  </data>
  <data name="emailerror_comment_senderadresse_xml" xml:space="preserve">
    <value>Sender address</value>
  </data>
  <data name="emailerror_comment_replyadresse_xml" xml:space="preserve">
    <value>Reply-to address</value>
  </data>
  <data name="emailerror_comment_copyadresse_xml" xml:space="preserve">
    <value>Copy address</value>
  </data>
  <data name="emailerror_comment_blindcopyadresse_xml" xml:space="preserve">
    <value>Blind copy address</value>
  </data>
  <data name="emailerror_comment_inscriptionsenderadresse_xml" xml:space="preserve">
    <value>Sender for registration email</value>
  </data>
  <data name="emailerror_comment_passwordcopyadresse_xml" xml:space="preserve">
    <value>Sender for forgotten password email</value>
  </data>
  <data name="kiosq_comment_weboperatorid_xml" xml:space="preserve">
    <value>Operator used by the KIOSQ</value>
  </data>
  <data name="kiosq_comment_filiereid_xml" xml:space="preserve">
    <value>Sector ID</value>
  </data>
  <data name="kiosq_comment_tarifidref_xml" xml:space="preserve">
    <value>Rate reference ID</value>
  </data>
  <data name="kiosq_comment_cbmodeidid_xml" xml:space="preserve">
    <value>Credit card mode ID</value>
  </data>
  <data name="kiosq_comment_postid_xml" xml:space="preserve">
    <value>Point of sale ID</value>
  </data>
  <data name="prisepacessurplan_comment_ismultizones_xml" xml:space="preserve">
    <value>Multizone sale</value>
  </data>
  <data name="createprofilabo_comment_webfiliereid_xml" xml:space="preserve">
    <value>Subscription - Create Profile - Sector</value>
  </data>
  <data name="createprofil_comment_mailunicity_xml" xml:space="preserve">
    <value>Unique email (0: No, 1: Yes, -1: Disable) for client profile</value>
  </data>
  <data name="createprofil_comment_webfiliereid_xml" xml:space="preserve">
    <value> Online Sales - Create Profile - Sector</value>
  </data>
  <data name="createprofillink_comment_mailunicity_xml " xml:space="preserve">
    <value>Email uniqueness for profile</value>
  </data>
  <data name="emailsupportingdocuments_comment_senderadresse_xml" xml:space="preserve">
    <value>Sender address</value>
  </data>
  <data name="emailsupportingdocuments_comment_receiveradresse_xml" xml:space="preserve">
    <value>Receiver address</value>
  </data>
  <data name="emailsupportingdocuments_comment_replyadresse_xml" xml:space="preserve">
    <value>Reply address</value>
  </data>
  <data name="emailsupportingdocuments_comment_copyadresse_xml" xml:space="preserve">
    <value>Copy address</value>
  </data>
  <data name="emailsupportingdocuments_comment_smtpclientip_xml" xml:space="preserve">
    <value>SMTP client server IP</value>
  </data>
  <data name="supportingdocuments_comment_sizeattachments_xml" xml:space="preserve">
    <value>Maximum upload size allowed</value>
  </data>
  <data name="edcampaign_comment_presta_xml" xml:space="preserve">
    <value>List of email providers</value>
  </data>
  <data name="edcampaign_comment_eccmkey_xml" xml:space="preserve">
    <value>ECCM key for integration</value>
  </data>
  <data name="specifique_comment_generernumerofacture_xml" xml:space="preserve">
    <value>Automatic invoice number generation</value>
  </data>
  <data name="specifique_comment_flagdesplacesdanspaiement_xml" xml:space="preserve">
    <value>Indicator to manage seats during payment</value>
  </data>
  <data name="specifique_comment_seanceidnotset_xml" xml:space="preserve">
    <value>Undefined session ID</value>
  </data>
  <data name="acomptes_comment_modespaiement_xml" xml:space="preserve">
    <value>Payment mode ID used for deposit</value>
  </data>
  <data name="abov2_comment_offreidhorsabo_xml" xml:space="preserve">
    <value>ID of the out-of-subscription offer available on the subscription platform</value>
  </data>
  <data name="paypalconnect_comment_username_xml" xml:space="preserve">
    <value>Username for PayPal Connect</value>
  </data>
  <data name="paypalconnect_comment_password_xml" xml:space="preserve">
    <value>Password for PayPal Connect</value>
  </data>
  <data name="facebook_comment_applicationsecret_xml" xml:space="preserve">
    <value>facebook_comment_applicationsecret_xml</value>
  </data>
  <data name="facebookdev_comment_applicationsecret_xml" xml:space="preserve">
    <value>Facebook dev application secret</value>
  </data>
  <data name="services_comment_opinion_order_xml" xml:space="preserve">
    <value>Display of the opinion order on the payment site (1 to activate, 0 to deactivate)</value>
  </data>
  <data name="coupefile_comment_futures_events_sessions_duration_xml" xml:space="preserve">
    <value>Duration of future events sessions</value>
  </data>
  <data name="coupefile_comment_message_commentaire_xml" xml:space="preserve">
    <value>Comment message</value>
  </data>
  <data name="boutique_comment_delaicachehome_xml" xml:space="preserve">
    <value>Cache time for home shop page</value>
  </data>
  <data name="boutique_comment_delaicachefamille_xml" xml:space="preserve">
    <value>Cache time for family page</value>
  </data>
  <data name="boutique_comment_delaicachesousfamille_xml " xml:space="preserve">
    <value>Cache time for sub-family page</value>
  </data>
  <data name="boutique_comment_delaicachedetail_xml" xml:space="preserve">
    <value>Cache time for detail page</value>
  </data>
  <data name="boutique_comment_delaicachemenu_xml" xml:space="preserve">
    <value>Cache time for menu</value>
  </data>
  <data name="revendeur_comment_ticketacapi_url_xml" xml:space="preserve">
    <value>URL for real-time interface with Ticketac vendor</value>
  </data>
  <data name="revendeur_comment_ticketacapi_user_xml" xml:space="preserve">
    <value>User ID for real-time interface with Ticketac</value>
  </data>
  <data name="revendeur_comment_ticketacapi_passw_xml" xml:space="preserve">
    <value>Password for real-time interface with Ticketac</value>
  </data>
  <data name="formule_comment_listeformuleid_xml" xml:space="preserve">
    <value>ID of the list of available formulas</value>
  </data>
  <data name="cbmodeidxfois_comment_idxfois_xml" xml:space="preserve">
    <value>Multiple payment index</value>
  </data>
  <data name="aexpta_comment_url_xml" xml:space="preserve">
    <value>URL for the Aexpta payment platform</value>
  </data>
  <data name="aexpta_comment_hmac_xml" xml:space="preserve">
    <value>HMAC key for secure transactions</value>
  </data>
  <data name="aexpta_comment_blowfish_xml" xml:space="preserve">
    <value>Blowfish key for encryption</value>
  </data>
  <data name="aexpta_comment_merchantid_xml" xml:space="preserve">
    <value>Merchant ID</value>
  </data>
  <data name="reabo_comment_formule_xml" xml:space="preserve">
    <value>Subscription renewal formula</value>
  </data>
  <data name="reabo_comment_dossieretatcheckabo_xml" xml:space="preserve">
    <value>File status for subscription check</value>
  </data>
  <data name="reabo_comment_updatefansonstructure_xml" xml:space="preserve">
    <value>Update fans on structure</value>
  </data>
  <data name="reabo_comment_interdireinfocompupdatefans_xml" xml:space="preserve">
    <value>Prohibit complementary info update for fans</value>
  </data>
  <data name="atos64_comment_paiementenxfois_xml" xml:space="preserve">
    <value>Multi-installment payment mode</value>
  </data>
  <data name="indiv_comment_openprofilacheteurpopup_xml" xml:space="preserve">
    <value>Display buyer's profile in popup</value>
  </data>
  <data name="indiv_comment_forcerloginlistemanifs_xml" xml:space="preserve">
    <value>Force login for event list</value>
  </data>
  <data name="indiv_comment_forcerloginpanier_xml" xml:space="preserve">
    <value>Force login for cart</value>
  </data>
  <data name="emailkiosk_comment_senderadresse_xml" xml:space="preserve">
    <value>Sender address</value>
  </data>
  <data name="emailkiosk_comment_replyadresse_xml" xml:space="preserve">
    <value>Reply address</value>
  </data>
  <data name="emailkiosk_comment_copyadresse_xml" xml:space="preserve">
    <value>Copy address</value>
  </data>
  <data name="emailkiosk_comment_trakingreceiver_xml" xml:space="preserve">
    <value>Tracking address</value>
  </data>
  <data name="emailkiosk_comment_overwriteuseradresse_xml" xml:space="preserve">
    <value>Overwrite user address</value>
  </data>
  <data name="emailinscription_comment_senderadresse_xml" xml:space="preserve">
    <value>Sender of the registration email</value>
  </data>
  <data name="emailinscription_comment_replyadresse_xml" xml:space="preserve">
    <value>Reply address for registration email</value>
  </data>
  <data name="emailinscription_comment_copyadresse_xml" xml:space="preserve">
    <value>Copy address for registration email</value>
  </data>
  <data name="emailseuilmini_comment_sendadresse_xml" xml:space="preserve">
    <value>Sender of the email</value>
  </data>
  <data name="emailseuilmini_comment_senderadresse_xml" xml:space="preserve">
    <value>Email sender</value>
  </data>
  <data name="emailseuilmini_comment_replyadresse_xml" xml:space="preserve">
    <value>Reply address</value>
  </data>
  <data name="emailseuilmini_comment_copyadresse_xml" xml:space="preserve">
    <value>Copy address</value>
  </data>
  <data name="revendeur_comment_duration_xml" xml:space="preserve">
    <value>Event duration in minutes for reseller (configurable request)</value>
  </data>
  <data name="revendeur_comment_message_commentaire_xml" xml:space="preserve">
    <value>Message for download from Fast Track</value>
  </data>
  <data name="smsefidem_comment_appid_xml" xml:space="preserve">
    <value>Application ID</value>
  </data>
  <data name="smsefidem_comment_login_xml" xml:space="preserve">
    <value>Login ID</value>
  </data>
  <data name="smsefidem_comment_passw_xml" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="crmexterne_comment_type_xml" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="crmexterne_comment_salt_xml" xml:space="preserve">
    <value>Salt</value>
  </data>
  <data name="crmexterne_comment_url_xml" xml:space="preserve">
    <value> URL</value>
  </data>
  <data name="unidy_comment_secret_xml" xml:space="preserve">
    <value> Secret</value>
  </data>
  <data name="unidy_comment_client_xml" xml:space="preserve">
    <value>Client</value>
    <comment> Client</comment>
  </data>
  <data name="unidy_comment_redirecturl_xml" xml:space="preserve">
    <value>Redirect URL</value>
  </data>
  <data name="unidy_comment_urltokenservice_xml" xml:space="preserve">
    <value>Token service URL</value>
  </data>
  <data name="unidy_comment_urluinfo_xml" xml:space="preserve">
    <value>User info URL</value>
  </data>
  <data name="virement_comment_hourslimitation_xml" xml:space="preserve">
    <value> Hours limitation</value>
  </data>
  <data name="assurancearteo_comment_senderadresse_xml" xml:space="preserve">
    <value>Sender of the email</value>
  </data>
  <data name="assurancearteo_comment_replyadresse_xml" xml:space="preserve">
    <value> réponses Reply address</value>
  </data>
  <data name="assurancearteo_comment_copyadresse_xml" xml:space="preserve">
    <value>Copy address</value>
  </data>
  <data name="assurancearteo_comment_smtpclientip_xml" xml:space="preserve">
    <value>SMTP client IP address</value>
  </data>
  <data name="pelecard_comment_currency_xml" xml:space="preserve">
    <value>Currency</value>
  </data>
  <data name="pelecard_comment_login_xml" xml:space="preserve">
    <value>Login ID</value>
  </data>
  <data name="pelecard_comment_terminalid_xml" xml:space="preserve">
    <value>Terminal ID</value>
  </data>
  <data name="pelecard_comment_password_xml" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="pelecard_comment_actiontype_xml" xml:space="preserve">
    <value>Action Type</value>
  </data>
  <data name="pelecard_comment_url_xml" xml:space="preserve">
    <value>URL</value>
  </data>
  <data name="pelecard_comment_acceptedlanguage_xml " xml:space="preserve">
    <value>Accepted language</value>
  </data>
  <data name="paypalapi_comment_currency_xml" xml:space="preserve">
    <value>Currency</value>
  </data>
  <data name="paypalapi_comment_accesstoken_xml" xml:space="preserve">
    <value>Access token</value>
  </data>
  <data name="paypalapi_comment_mode_xml" xml:space="preserve">
    <value>Mode</value>
  </data>
  <data name="paramprintathome_comment_ticketeos_urlkombiticket_xml" xml:space="preserve">
    <value>KombiTicket URL</value>
  </data>
  <data name="paramprintathome_comment_ticketeos_creduser_xml" xml:space="preserve">
    <value>User credential</value>
  </data>
  <data name="paramprintathome_comment_ticketeos_credpassw_xml" xml:space="preserve">
    <value>User password</value>
  </data>
  <data name="paramprintathome_comment_ticketeos_token_xml" xml:space="preserve">
    <value>Token</value>
  </data>
  <data name="paramprintathome_comment_ticketeos_systemid_xml" xml:space="preserve">
    <value>System ID</value>
  </data>
  <data name="paramprintathome_comment_ticketeos_organizerid_xml" xml:space="preserve">
    <value>Organizer ID</value>
  </data>
  <data name="emailpassword_comment_senderadresse_xml" xml:space="preserve">
    <value>Sender of the email</value>
  </data>
  <data name="emailpassword_comment_replyadresse_xml" xml:space="preserve">
    <value>Reply address</value>
  </data>
  <data name="emailpassword_comment_copyadresse_xml" xml:space="preserve">
    <value>Copy address </value>
  </data>
  <data name="contraintesventes_comment_restrictiontarifparfichier_xml" xml:space="preserve">
    <value>Pricing restriction per file</value>
  </data>
  <data name="customerarea_comment_version_xml" xml:space="preserve">
    <value>Customer area version</value>
  </data>
  <data name="customerarea_comment_checkinfocomplogin_xml" xml:space="preserve">
    <value>InfoComp Login</value>
  </data>
  <data name="customerarea_comment_listinfocompdisponible_xml" xml:space="preserve">
    <value>List of available infocomp</value>
  </data>
  <data name="createdossierproduits_comment_filiereid_xml" xml:space="preserve">
    <value>Subscription - Create Profile - Sector</value>
  </data>
  <data name="cbmodeid_comment_xfois_xml" xml:space="preserve">
    <value>Payment method ID for installments</value>
  </data>
  <data name="cbmodeid_comment_sofortberweisungde_xml" xml:space="preserve">
    <value>Payment method ID SOFORT</value>
  </data>
  <data name="cbmodeid_comment_id_xml" xml:space="preserve">
    <value>Payment method ID CB</value>
  </data>
  <data name="cbmodeid_comment_mastercard_xml" xml:space="preserve">
    <value>Payment method ID MasterCard</value>
  </data>
  <data name="cbmodeid_comment_visa_xml" xml:space="preserve">
    <value>9930002</value>
  </data>
  <data name="cbmodeid_comment_ecard_xml" xml:space="preserve">
    <value>Payment method ID E_CARD</value>
  </data>
  <data name="cbmodeid_comment_paypal_xml" xml:space="preserve">
    <value>Payment method ID PAYPAL</value>
  </data>
  <data name="cbmodeid_comment_cvco_xml" xml:space="preserve">
    <value>Payment method ID CVCO</value>
  </data>
  <data name="cbmodeid_comment_multi_xml" xml:space="preserve">
    <value>Payment method ID MULTI</value>
  </data>
  <data name="cbmodeid_comment_sofort_xml" xml:space="preserve">
    <value>Payment method ID SOFORT</value>
  </data>
  <data name="cbmodeid_comment_pa15_sofortberweisungde_xml" xml:space="preserve">
    <value>Payment method ID SOFORT</value>
  </data>
  <data name="cbmodeid_comment_na_xml" xml:space="preserve">
    <value>Payment method ID Invoice or Free</value>
  </data>
  <data name="cbmodeid_comment_pa_cb_xml" xml:space="preserve">
    <value>Payment method ID CB</value>
  </data>
  <data name="cbmodeid_comment_pa_paypal_xml" xml:space="preserve">
    <value>Payment method ID PAYPAL</value>
  </data>
  <data name="cbmodeid_comment_pa_sofortberweisungde_xml" xml:space="preserve">
    <value>Payment method ID SOFORT</value>
  </data>
  <data name="cbmodeid_comment_pa_visa_xml" xml:space="preserve">
    <value>Payment method ID VISA</value>
  </data>
  <data name="toast_message_connection_error_title" xml:space="preserve">
    <value>Connection Error</value>
  </data>
  <data name="toast_message_connection_error_body" xml:space="preserve">
    <value>Connection failed. Please select another structure.</value>
  </data>
  <data name="button_translations_terms" xml:space="preserve">
    <value>Widget Translation</value>
  </data>
  <data name="button_sections" xml:space="preserve">
    <value>Sections Translation</value>
  </data>
  <data name="button_variables" xml:space="preserve">
    <value>Variables Translation</value>
  </data>
  <data name="modal_title_edit_section" xml:space="preserve">
    <value>Edit section</value>
  </data>
  <data name="confirm_delete" xml:space="preserve">
    <value>Are you sure you want to delete the variable  &lt;strong&gt;'{0}'&lt;/strong&gt;?</value>
  </data>
  <data name="delete_success" xml:space="preserve">
    <value>Variable &lt;strong&gt;'{0}'&lt;/strong&gt; deleted successfully.</value>
  </data>
  <data name="delete_error" xml:space="preserve">
    <value>Error deleting variable &lt;strong&gt;'{0}': {1}&lt;/strong&gt;</value>
  </data>
  <data name="confirm_delete_translation_area" xml:space="preserve">
    <value>Are you sure you want to delete the variable '{0}'?</value>
  </data>
  <data name="edit_partenaire" xml:space="preserve">
    <value>Edit a partner</value>
  </data>
  <data name="added_sections" xml:space="preserve">
    <value> Add Sections</value>
  </data>
  <data name="mandatory_sections" xml:space="preserve">
    <value>Mandatory Configuration</value>
  </data>
  <data name="advanced_configuration" xml:space="preserve">
    <value>Additional sections</value>
  </data>
  <data name="confirm_dialog_yes_button" xml:space="preserve">
    <value>Yes </value>
  </data>
  <data name="confirm_dialog_no_button" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="time_warning_message" xml:space="preserve">
    <value>The file will be deleted in: {0} minute(s) and {1} second(s). Do you want to proceed?</value>
  </data>
  <data name="time_warning_title" xml:space="preserve">
    <value>Time Warning</value>
  </data>
  <data name="toast_message_title_file_deletion" xml:space="preserve">
    <value>File Deletion</value>
  </data>
  <data name="toast_message_file_deletion" xml:space="preserve">
    <value>The temporary file has been deleted because the time is up.</value>
  </data>
  <data name="selected_variables" xml:space="preserve">
    <value>Selected Variables</value>
  </data>
  <data name="translation_choose_variables" xml:space="preserve">
    <value>Choose variables</value>
  </data>
  <data name="translation_variable_fully_selected" xml:space="preserve">
    <value>Fully Selected</value>
  </data>
  <data name="translation_variable_partially_selected" xml:space="preserve">
    <value>Partially Selected</value>
  </data>
  <data name="translation_variable_not_selected" xml:space="preserve">
    <value>Not Selected</value>
  </data>
  <data name="Field_Validation_SingleEmail" xml:space="preserve">
    <value>The field can contain only one email address</value>
  </data>
  <data name="Field_Validation_ReplyAddress_SingleEmail" xml:space="preserve">
    <value>The REPLYADRESSE field can contain only one email address</value>
  </data>
  <data name="Field_Validation_InvalidEmail" xml:space="preserve">
    <value>Invalid email address</value>
  </data>
  <data name="Field_Validation_InvalidEmail_WithDetails" xml:space="preserve">
    <value>Invalid email address: {0}</value>
  </data>
  <data name="Field_Validation_DuplicateEmail" xml:space="preserve">
    <value>Duplicate email address detected.</value>
  </data>
  <data name="Field_Validation_DuplicateEmail_WithDetails" xml:space="preserve">
    <value>Duplicate email addresses detected: {0}</value>
  </data>
  <data name="close_modal" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="toast_message_title_success" xml:space="preserve">
    <value> Success</value>
  </data>
  <data name="toast_message_title_error" xml:space="preserve">
    <value>Erreur</value>
  </data>
  <data name="toast_message_form_submission_failed" xml:space="preserve">
    <value> Form submission failed: {0}</value>
  </data>
  <data name="toast_message_form_validation_failed" xml:space="preserve">
    <value>he form contains validation errors</value>
  </data>
  <data name="gestion-webtracing" xml:space="preserve">
    <value>WebTracing Management</value>
  </data>
  <data name="username_nom_specifie_webtracing" xml:space="preserve">
    <value>Unspecified</value>
  </data>
  <data name="toast_message_structure_failed" xml:space="preserve">
    <value>Failed to load structure data</value>
  </data>
  <data name="toast_message_title_delete_structure_success" xml:space="preserve">
    <value>Structure Deletion Successful</value>
  </data>
  <data name="toast_message_delete_success" xml:space="preserve">
    <value>The structure was successfully deleted.</value>
  </data>
  <data name="toast_message_title_delete_structure_error" xml:space="preserve">
    <value>Structure Deletion Error</value>
  </data>
  <data name="toast_message_delete_structure_failed" xml:space="preserve">
    <value>Failed to delete structure</value>
  </data>
  <data name="webtracing_list_title" xml:space="preserve">
    <value>List of Structures with WebTracing Connections</value>
  </data>
  <data name="webtracing_no_structure_found" xml:space="preserve">
    <value>No structure or connection found.</value>
  </data>
  <data name="webtracing_deleted" xml:space="preserve">
    <value>Deleted</value>
  </data>
  <data name="webtracing_not_deleted" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="webtracing_unspecified" xml:space="preserve">
    <value>Unspecified</value>
  </data>
  <data name="webtracing_column_name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="webtracing_column_deleted" xml:space="preserve">
    <value>Deleted</value>
  </data>
  <data name="webtracing_column_deletion_date" xml:space="preserve">
    <value>Deletion Date</value>
  </data>
  <data name="webtracing_column_deleted_by" xml:space="preserve">
    <value>Deleted By</value>
  </data>
  <data name="webtracing_column_database" xml:space="preserve">
    <value>Database</value>
  </data>
  <data name="webtracing_column_start_date" xml:space="preserve">
    <value>Start Date </value>
  </data>
  <data name="webtracing_column_stop_date" xml:space="preserve">
    <value>End Date</value>
  </data>
  <data name="webtracing_column_actions" xml:space="preserve">
    <value>Actions</value>
  </data>
  <data name="webtracing_button_deleted" xml:space="preserve">
    <value>Deleted</value>
  </data>
  <data name="webtracing_button_delete" xml:space="preserve">
    <value>To be deleted</value>
  </data>
  <data name="role-management" xml:space="preserve">
    <value>Role Management</value>
  </data>
  <data name="modal_success_message" xml:space="preserve">
    <value>coupons have been successfully generated and saved in the database.</value>
  </data>
  <data name="rolemanagement_create_role" xml:space="preserve">
    <value>Create a new role</value>
  </data>
  <data name="new-module" xml:space="preserve">
    <value>Create a new module</value>
  </data>
  <data name="rolemanagement_create_group" xml:space="preserve">
    <value>Create a group</value>
  </data>
  <data name="role_create_new" xml:space="preserve">
    <value>Create a new role</value>
  </data>
  <data name="rolemanagement_create_module" xml:space="preserve">
    <value>Create a Module</value>
  </data>
  <data name="rolemanagement_groupname" xml:space="preserve">
    <value>Group Name</value>
  </data>
  <data name="rolemanagement_groupid" xml:space="preserve">
    <value>Group ID</value>
  </data>
  <data name="rolemanagement_modulename" xml:space="preserve">
    <value>Module Name</value>
  </data>
  <data name="rolemanagement_modulecomment" xml:space="preserve">
    <value>Comment</value>
  </data>
  <data name="rolemanagement_rolename" xml:space="preserve">
    <value>Role Name</value>
  </data>
  <data name="rolemanagement_canread" xml:space="preserve">
    <value>Can Read</value>
  </data>
  <data name="rolemanagement_cancreate" xml:space="preserve">
    <value>Can Create</value>
  </data>
  <data name="rolemanagement_canupdate" xml:space="preserve">
    <value>Can Update</value>
  </data>
  <data name="rolemanagement_candelete" xml:space="preserve">
    <value>Can Delete</value>
  </data>
  <data name="rolemanagement_level" xml:space="preserve">
    <value>Level</value>
  </data>
  <data name="rolemanagement_title" xml:space="preserve">
    <value>Role and Module Management</value>
  </data>
  <data name="rolemanagement_loading" xml:space="preserve">
    <value>Please wait while loading data...</value>
  </data>
  <data name="rolemanagement_module" xml:space="preserve">
    <value>Module</value>
  </data>
  <data name="rolemanagement_role" xml:space="preserve">
    <value>Role</value>
  </data>
  <data name="rolemanagement_no_module" xml:space="preserve">
    <value>No Role</value>
  </data>
  <data name="rolemanagement_no_module_or_role" xml:space="preserve">
    <value>No Module or Role for {0}</value>
  </data>
  <data name="rolemanagement_assign_roles" xml:space="preserve">
    <value>Role Assignment</value>
  </data>
  <data name="rolemanagement_modules" xml:space="preserve">
    <value>Modules</value>
  </data>
  <data name="rolemanagement_ad_groups" xml:space="preserve">
    <value>AD Groups</value>
  </data>
  <data name="rolemanagement_roles" xml:space="preserve">
    <value>Roles</value>
  </data>
  <data name="rolemanagement_choose_option" xml:space="preserve">
    <value>Choose option</value>
  </data>
  <data name="rolemanagement_assign" xml:space="preserve">
    <value>Assign</value>
  </data>
  <data name="error_create_group_title" xml:space="preserve">
    <value>Error creating group</value>
  </data>
  <data name="error_create_group_message" xml:space="preserve">
    <value>Unable to create group {0}. Please try again.</value>
  </data>
  <data name="Serror_create_module_title" xml:space="preserve">
    <value>Error creating module</value>
  </data>
  <data name="error_create_module_message" xml:space="preserve">
    <value>Unable to create module {0}. Please try again.</value>
  </data>
  <data name="error_create_role_title" xml:space="preserve">
    <value>Error creating role</value>
  </data>
  <data name="error_create_role_message" xml:space="preserve">
    <value>Unable to create role {0}. Please try again.</value>
  </data>
  <data name="error_loading_data_title" xml:space="preserve">
    <value>Error loading data</value>
  </data>
  <data name="error_loading_data_message" xml:space="preserve">
    <value>An error occurred while loading data: {0}</value>
  </data>
  <data name="success_roles_assigned_title" xml:space="preserve">
    <value>Roles assigned successfully</value>
  </data>
  <data name="success_roles_assigned_message" xml:space="preserve">
    <value>Roles have been successfully assigned.</value>
  </data>
  <data name="error_assign_roles_title" xml:space="preserve">
    <value>Error assigning roles</value>
  </data>
  <data name="error_assign_roles_message" xml:space="preserve">
    <value>Failed to assign roles: {0}</value>
  </data>
  <data name="error_group_exists" xml:space="preserve">
    <value>The group {0} already exists.</value>
  </data>
  <data name="error_module_exists" xml:space="preserve">
    <value>The module {0} already exists.</value>
  </data>
  <data name="error_role_exists" xml:space="preserve">
    <value>The role {0} already exists.</value>
  </data>
  <data name="choisi_structure" xml:space="preserve">
    <value>Chosen</value>
  </data>
  <data name="error_search_partners_title" xml:space="preserve">
    <value>Error searching for partners</value>
  </data>
  <data name="no_roles" xml:space="preserve">
    <value>No roles</value>
  </data>
  <data name="label_roles" xml:space="preserve">
    <value>Roles</value>
  </data>
  <data name="label_structures" xml:space="preserve">
    <value>Structures</value>
  </data>
  <data name=" no_structures" xml:space="preserve">
    <value>No structures </value>
  </data>
  <data name="no_partner_structures" xml:space="preserve">
    <value>No structures</value>
  </data>
  <data name="Stradd_missing_keys" xml:space="preserve">
    <value>Add missing keys</value>
  </data>
  <data name="toast_add_missing_keys_success" xml:space="preserve">
    <value>Missing keys added successfully</value>
  </data>
  <data name="add_missing_keys_title" xml:space="preserve">
    <value>Add missing keys</value>
  </data>
  <data name="add_missing_keys_description" xml:space="preserve">
    <value>Some keys are missing. Do you want to add them?</value>
  </data>
  <data name="for_all_languages" xml:space="preserve">
    <value>For all languages</value>
  </data>
  <data name="for_this_language" xml:space="preserve">
    <value>For this language</value>
  </data>
  <data name="add_missing_keys" xml:space="preserve">
    <value>Add missing keys</value>
  </data>
  <data name="payment_platform_translation" xml:space="preserve">
    <value>Translation of the payment platform</value>
  </data>
  <data name="copy_to_test" xml:space="preserve">
    <value>Copy to TEST</value>
  </data>
  <data name="copy_to_prod" xml:space="preserve">
    <value>Copy to PROD</value>
  </data>
  <data name="copy_to_prod_confirmation" xml:space="preserve">
    <value>Are you sure you want to copy the data to PROD?</value>
  </data>
  <data name="copy_to_test_confirmation" xml:space="preserve">
    <value>Are you sure you want to copy the data to TEST?</value>
  </data>
  <data name="copy_to_prod_title" xml:space="preserve">
    <value>Copy to PROD</value>
  </data>
  <data name="copy_to_test_title" xml:space="preserve">
    <value>Copy to TEST</value>
  </data>
  <data name="toast_copy_to_test_success" xml:space="preserve">
    <value>Copy DEV to TEST successful</value>
  </data>
  <data name="toast_copy_to_prod_success" xml:space="preserve">
    <value>Copy DEV to PROD successful</value>
  </data>
  <data name="admin_platform_translation" xml:space="preserve">
    <value>Platform Admin Translation</value>
  </data>
  <data name="cbmodeid_comment_pa_paybox_xml" xml:space="preserve">
    <value>payment, two accounts for a provider</value>
  </data>
  <data name="cbmodeid_comment_pa_paybox_url_redirection_xml" xml:space="preserve">
    <value>Redirect URL to the payment provider</value>
  </data>
  <data name="cbmodeid_comment_pa_paybox_code_devise_xml" xml:space="preserve">
    <value>Currency code of the site to be sent to the provider</value>
  </data>
  <data name="cbmodeid_comment_pa_paybox_identifiant_structure_xml" xml:space="preserve">
    <value>Structure identifier</value>
  </data>
  <data name="cbmodeid_comment_pa_paybox_code_site_xml" xml:space="preserve">
    <value>Site code (provided by PAYBOX</value>
  </data>
  <data name="cbmodeid_comment_pa_paybox_rang_site_xml" xml:space="preserve">
    <value>Site rank (provided by PAYBOX)</value>
  </data>
  <data name="cbmodeid_comment_pa_paybox2_xml" xml:space="preserve">
    <value>payment, two accounts for a provide</value>
  </data>
  <data name="cbmodeid_comment_pa_paybox2_url_redirection_xml" xml:space="preserve">
    <value>Redirect URL to the payment provide</value>
  </data>
  <data name="cbmodeid_comment_pa_paybox2_code_devise_xml" xml:space="preserve">
    <value>Currency code of the site to be sent to the provider</value>
  </data>
  <data name="cbmodeid_comment_pa_paybox2_code_site_xml" xml:space="preserve">
    <value>Site code (provided by PAYBOX)</value>
  </data>
  <data name="cbmodeid_comment_pa_paybox2_rang_site_xml" xml:space="preserve">
    <value>Site rank (provided by PAYBOX)</value>
  </data>
  <data name="cbmodeid_comment_pa_paybox2_identifiant_structure_xml" xml:space="preserve">
    <value>Structure identifier</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac1_xml" xml:space="preserve">
    <value>payment with HMAC, two accounts for a provider</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac1_url_redirection_xml" xml:space="preserve">
    <value>Redirect URL to the payment provider</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac1_code_devise_xml" xml:space="preserve">
    <value>Currency code of the site to be sent to the provider</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac1_code_site_xml" xml:space="preserve">
    <value>Site code (provided by PAYBOXHMAC)</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac1_rang_site_xml" xml:space="preserve">
    <value>Site rank (provided by PAYBOXHMAC)</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac1_identifiant_structure_xml" xml:space="preserve">
    <value>Structure identifier</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac1_hmac_xml" xml:space="preserve">
    <value>HMAC key used to secure the transaction</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac2_xml" xml:space="preserve">
    <value>payment with HMAC, two accounts for a provider</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac2_url_redirection_xml" xml:space="preserve">
    <value>Redirect URL to the payment provider</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac2_url_redirection_old_xml" xml:space="preserve">
    <value>Old redirect URL to the payment provider</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac2_code_devise_xml" xml:space="preserve">
    <value>Currency code of the site to be sent to the provider</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac2_code_site_xml" xml:space="preserve">
    <value>Site code (provided by PAYBOXHMAC)</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac2_rang_site_xml" xml:space="preserve">
    <value>Site rank (provided by PAYBOXHMAC)</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac2_identifiant_structure_xml" xml:space="preserve">
    <value>Structure identifier</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac2_url_acquit_xml" xml:space="preserve">
    <value>Acknowledgment URL for the transaction</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac2_hmac_xml" xml:space="preserve">
    <value>HMAC key used to secure the transaction</value>
  </data>
  <data name="cbmodeid_comment_pa_cybermuth1_xml" xml:space="preserve">
    <value>Payment via CYBERMUTH, specific integration</value>
  </data>
  <data name="cbmodeid_comment_pa_cybermuth1_url_redirection_xml" xml:space="preserve">
    <value>Redirect URL to the payment provider</value>
  </data>
  <data name="cbmodeid_comment_pa_cybermuth1_code_devise_xml" xml:space="preserve">
    <value>Currency code of the site to be sent to the provider</value>
  </data>
  <data name="cbmodeid_comment_pa_cybermuth1_identifiant_structure_xml" xml:space="preserve">
    <value>Structure identifier</value>
  </data>
  <data name="cbmodeid_comment_pa_cybermuth1_code_site_xml" xml:space="preserve">
    <value> Site code (provided by CYBERMUTH)</value>
  </data>
  <data name="cbmodeid_comment_pa_cybermuth1_version_xml" xml:space="preserve">
    <value>Version of the protocol used</value>
  </data>
  <data name="cbmodeid_comment_pa_cybermuth1_key_xml" xml:space="preserve">
    <value>Security key for transactions</value>
  </data>
  <data name="cbmodeid_comment_pa_cybermuth1_langage_xml" xml:space="preserve">
    <value>Language used for the transaction</value>
  </data>
  <data name="cbmodeid_comment_pa_cybermuth1_hashalgo_xml" xml:space="preserve">
    <value>Hash algorithm used for security</value>
  </data>
  <data name="cbmodeid_comment_pa_linkapiaccuse_xml" xml:space="preserve">
    <value>Link used to request the card capture after successful reception</value>
  </data>
  <data name="cbmodeid_comment_pa_apipassword_xml" xml:space="preserve">
    <value>Password used with LINKAPIACCUSE</value>
  </data>
  <data name="cbmodeid_comment_pa_apilogin_xml" xml:space="preserve">
    <value>User used with LINKAPIACCUSE</value>
  </data>
  <data name="cbmodeid_comment_pa_s10reference_xml" xml:space="preserve">
    <value>Fill in this key if the client is undergoing a simplified migration with Atos: only include this key if the client’s contract does not start with 0210</value>
  </data>
  <data name="cbmodeid_comment_pa_versionkey_xml" xml:space="preserve">
    <value>Version number of the key used (usually = 1, see client’s back-office for the provider)</value>
  </data>
  <data name="cbmodeid_comment_pa_key_xml" xml:space="preserve">
    <value>Access key (client’s back-office for the provider)</value>
  </data>
  <data name="cbmodeid_comment_pa_interfaceversion_xml" xml:space="preserve">
    <value>Payment provider version</value>
  </data>
  <data name="cbmodeid_comment_pa_autoripuiscapture_xml" xml:space="preserve">
    <value>If O, proceed with an authorization THEN the capture after validation. In this case, the next 2 keys must be present</value>
  </data>
  <data name="cbmodeid_comment_pa_password_xml" xml:space="preserve">
    <value>Live or TEST Secret Key to be found in the back-office</value>
  </data>
  <data name="cbmodeid_comment_pa_autoripuiscapture_true_false_xml" xml:space="preserve">
    <value>If 1, authorization is done then capture after validation, otherwise no authorization</value>
  </data>
  <data name="cbmodeid_comment_pa_smsdefaultregion_xml" xml:space="preserve">
    <value>The SMSDEFAULTREGION key corresponds to the default country code for SMS campaigns</value>
  </data>
  <data name="cbmodeid_comment_pa_webhooksalt_xml" xml:space="preserve">
    <value>Key provided by Sharegroop to generate the signature during the notification</value>
  </data>
  <data name="cbmodeid_comment_pa_rang_xml" xml:space="preserve">
    <value>Site rank</value>
  </data>
  <data name="modifier-temps-panier" xml:space="preserve">
    <value>Cart timer</value>
  </data>
  <data name="Modifier_Temps_Panier" xml:space="preserve">
    <value>Edit cart time</value>
  </data>
  <data name="Temps_Expiration_Panier" xml:space="preserve">
    <value>Ablaufzeit des Warenkorbs (für den Benutzer in Indiv sichtbar)</value>
  </data>
  <data name="Saisir_Temps_Expiration" xml:space="preserve">
    <value>Enter the time before cart expiration (1-1430 minutes).</value>
  </data>
  <data name="Delai_Deflag" xml:space="preserve">
    <value>Deflag delay (automatic)</value>
  </data>
  <data name="Delai_Deflag_Description" xml:space="preserve">
    <value>Deflag delay = Expiration time + 10 minutes (automatically calculated).</value>
  </data>
  <data name="retour_liste_struture" xml:space="preserve">
    <value>Back</value>
  </data>
  <data name="config_value_exceedsDB_error" xml:space="preserve">
    <value>The value(ConfigIni) must not be greater than the DB value.</value>
  </data>
  <data name="common_error" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="temps_panier_saved_successfully" xml:space="preserve">
    <value>Cart time saved successfully</value>
  </data>
  <data name="temps_panier_save_error" xml:space="preserve">
    <value>Error saving cart time: {0}</value>
  </data>
  <data name="delai_deflag_null_error" xml:space="preserve">
    <value>Deflagration delay is null</value>
  </data>
  <data name="panier_expiration_inferieure_deflag" xml:space="preserve">
    <value>The expiration time ({0}) cannot be less than the deflagration value ({1}).</value>
  </data>
  <data name="gestion-coupons-promo" xml:space="preserve">
    <value>Coupon manager</value>
  </data>
  <data name="no_profil_message" xml:space="preserve">
    <value>No buyer profile available.</value>
  </data>
  <data name="back_button" xml:space="preserve">
    <value>Back</value>
  </data>
  <data name="general_title" xml:space="preserve">
    <value>Promotional Coupon Management - Structure</value>
  </data>
  <data name="table_header_id" xml:space="preserve">
    <value>ID</value>
  </data>
  <data name="table_header_label" xml:space="preserve">
    <value>Label</value>
  </data>
  <data name="table_header_last_name" xml:space="preserve">
    <value>Last Name</value>
  </data>
  <data name="table_header_first_name" xml:space="preserve">
    <value>FirstName</value>
  </data>
  <data name="table_header_creation_date" xml:space="preserve">
    <value>Create Date </value>
  </data>
  <data name="table_header_coupon_status" xml:space="preserve">
    <value>Coupon Status</value>
  </data>
  <data name="table_header_actions" xml:space="preserve">
    <value>Actions</value>
  </data>
  <data name="coupon_status_active" xml:space="preserve">
    <value>active coupon(s)</value>
  </data>
  <data name="coupon_status_expired" xml:space="preserve">
    <value>Coupon status expired</value>
  </data>
  <data name="coupon_status_none" xml:space="preserve">
    <value>No coupon</value>
  </data>
  <data name="button_add_coupons" xml:space="preserve">
    <value>Add coupons</value>
  </data>
  <data name="button_view_coupons" xml:space="preserve">
    <value>View coupons</value>
  </data>
  <data name="button_export" xml:space="preserve">
    <value>Expert</value>
  </data>
  <data name="button_generate" xml:space="preserve">
    <value>Generate coupons</value>
  </data>
  <data name="button_close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="modal_create_title" xml:space="preserve">
    <value>Create promotional coupons for</value>
  </data>
  <data name="modal_number_of_coupons" xml:space="preserve">
    <value>Number of coupons to generate</value>
  </data>
  <data name="modal_max_coupons_message" xml:space="preserve">
    <value>You can generate up to 20 coupons at once</value>
  </data>
  <data name="modal_start_date" xml:space="preserve">
    <value>Start date</value>
  </data>
  <data name="modal_start_date_error" xml:space="preserve">
    <value>The start date must be today or in the future</value>
  </data>
  <data name="modal_end_date" xml:space="preserve">
    <value>Common expiration date</value>
  </data>
  <data name="modal_end_date_error" xml:space="preserve">
    <value>The expiration date must be after the start date</value>
  </data>
  <data name="modal_generating" xml:space="preserve">
    <value>Generating...</value>
  </data>
  <data name="modal_coupons_title" xml:space="preserve">
    <value>Counpons For</value>
  </data>
  <data name="tab_active_coupons" xml:space="preserve">
    <value>Active coupons</value>
  </data>
  <data name="tab_expired_coupons" xml:space="preserve">
    <value>Expired coupons</value>
  </data>
  <data name="tab_all_coupons" xml:space="preserve">
    <value>All coupons</value>
  </data>
  <data name="tab_no_coupons" xml:space="preserve">
    <value>No coupons found for this profile.</value>
  </data>
  <data name="coupon_list_code" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="coupon_list_creation_date" xml:space="preserve">
    <value>Creation date</value>
  </data>
  <data name="coupon_list_start_date" xml:space="preserve">
    <value>Start date</value>
  </data>
  <data name="coupon_list_expiration_date" xml:space="preserve">
    <value>Expiration date</value>
  </data>
  <data name="coupon_list_expires_in" xml:space="preserve">
    <value>Expires in</value>
  </data>
  <data name="coupon_list_expired_since" xml:space="preserve">
    <value>Expired since</value>
  </data>
  <data name="coupon_list_status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="coupon_list_active" xml:space="preserve">
    <value>Active</value>
  </data>
  <data name="coupon_list_expired" xml:space="preserve">
    <value>day(s)</value>
  </data>
  <data name="coupon_list_days" xml:space="preserve">
    <value>Day (s)</value>
  </data>
  <data name="coupon_list_no_active_coupons" xml:space="preserve">
    <value>No active coupons</value>
  </data>
  <data name="coupon_list_no_expired_coupons" xml:space="preserve">
    <value>No expired coupons</value>
  </data>
  <data name="button_export_these_coupons" xml:space="preserve">
    <value>Export these coupons</value>
  </data>
  <data name="modal_success_title" xml:space="preserve">
    <value>Promotional coupons successfully generated for</value>
  </data>
  <data name="button_exporting" xml:space="preserve">
    <value>Exporting...</value>
  </data>
  <data name="coupon_aucun_oupon_cree" xml:space="preserve">
    <value>No coupon could be created. Please try again.</value>
  </data>
  <data name="coupon_erreur_creation" xml:space="preserve">
    <value>Error while creating coupons: {0}</value>
  </data>
  <data name="coupon_creation_succes" xml:space="preserve">
    <value>{0} coupon(s) successfully created for {1}.</value>
  </data>
  <data name="create_coupon_attention" xml:space="preserve">
    <value>Attention</value>
  </data>
  <data name="profil_acheteur_non_valide" xml:space="preserve">
    <value>Invalid buyer profile.</value>
  </data>
  <data name="erreur_chargement_donnees" xml:space="preserve">
    <value>"Error loading data: {0}</value>
  </data>
  <data name="erreur_chargement_coupons" xml:space="preserve">
    <value>Error loading coupons: {0}</value>
  </data>
  <data name="erreur_exportation_coupons" xml:space="preserve">
    <value>Error exporting coupons: {0}</value>
  </data>
  <data name="coupon_export_success" xml:space="preserve">
    <value>Coupons for {0} have been successfully exported.</value>
  </data>
  <data name="aucun_coupon_exporter" xml:space="preserve">
    <value>No coupons to export for {0}.</value>
  </data>
  <data name="erreur_telechargement_fichier" xml:space="preserve">
    <value>Error downloading file: {0}</value>
  </data>
  <data name="nombre_coupons_range" xml:space="preserve">
    <value>The number of coupons</value>
  </data>
  <data name="nombre_coupons_required" xml:space="preserve">
    <value>The number of coupons is required.</value>
  </data>
  <data name="date_debut_required" xml:space="preserve">
    <value>The start date is required.</value>
  </data>
  <data name="date_expiration_required" xml:space="preserve">
    <value>The expiration date is required.</value>
  </data>
  <data name="config_keyuniquecodepromo_missing" xml:space="preserve">
    <value>The configuration key 'keyUniqueCodePromo' is missing or empty</value>
  </data>
  <data name="coupon_list_no_used_coupons" xml:space="preserve">
    <value>Coupon list – No used coupons</value>
  </data>
  <data name="coupon_status_used" xml:space="preserve">
    <value>Coupon status used</value>
  </data>
  <data name="tab_used_coupons" xml:space="preserve">
    <value>Used coupons</value>
  </data>
  <data name="coupon_list_commande_id" xml:space="preserve">
    <value>Order ID</value>
  </data>
  <data name="coupon_list_used_date" xml:space="preserve">
    <value>Used date</value>
  </data>
  <data name="modal_prefix" xml:space="preserve">
    <value>Prefix</value>
  </data>
  <data name="modal_example_code" xml:space="preserve">
    <value>Example code</value>
  </data>
  <data name="modal_format_explanation" xml:space="preserve">
    <value>Format: prefix + 2 digits + 3 letters</value>
  </data>
  <data name="coupon_list_used" xml:space="preserve">
    <value>Coupon used</value>
  </data>
  <data name="modal_prefix_min_length" xml:space="preserve">
    <value>The prefix must contain at least 2 characters</value>
  </data>
  <data name="service-inclusion-exclusion" xml:space="preserve">
    <value>Inclusion/Exclusion</value>
  </data>
  <data name="services_inclusion_exclusion" xml:space="preserve">
    <value>Services Inclusion and Exclusion</value>
  </data>
  <data name="services_inclusion_exclusion_info" xml:space="preserve">
    <value>Manage included and excluded structures for different services in PREPROD and PROD environments.</value>
  </data>
  <data name="creation_commande" xml:space="preserve">
    <value>Order Creation</value>
  </data>
  <data name="edition_commande" xml:space="preserve">
    <value>Order Edition</value>
  </data>
  <data name="paiement_commande" xml:space="preserve">
    <value>Order Payment</value>
  </data>
  <data name="envoi_mail" xml:space="preserve">
    <value>Email Sending</value>
  </data>
  <data name="preprod_environment" xml:space="preserve">
    <value>PREPROD Environment</value>
  </data>
  <data name="prod_environment" xml:space="preserve">
    <value>PROD Environment</value>
  </data>
  <data name="structure_id" xml:space="preserve">
    <value>Structure ID</value>
  </data>
  <data name="enter_structure_id" xml:space="preserve">
    <value>Enter structure ID</value>
  </data>
  <data name="inclusion" xml:space="preserve">
    <value>Inclusion</value>
  </data>
  <data name="exclusion" xml:space="preserve">
    <value>Exclusion</value>
  </data>
  <data name="add_structure" xml:space="preserve">
    <value>Add Structure</value>
  </data>
  <data name="inclusions" xml:space="preserve">
    <value>Inclusions</value>
  </data>
  <data name="exclusions" xml:space="preserve">
    <value>Exclusions</value>
  </data>
  <data name="no_structures" xml:space="preserve">
    <value>No structures</value>
  </data>
  <data name="common_warning" xml:space="preserve">
    <value>Warning</value>
  </data>
  <data name="common_info" xml:space="preserve">
    <value>Information</value>
  </data>
  <data name="structure_added_successfully" xml:space="preserve">
    <value>Structure added successfully</value>
  </data>
  <data name="structure_already_exists" xml:space="preserve">
    <value>Structure already exists</value>
  </data>
  <data name="structure_removed_successfully" xml:space="preserve">
    <value>Structure removed successfully</value>
  </data>
  <data name="structure_not_found" xml:space="preserve">
    <value>Structure not found</value>
  </data>
  <data name="file_does_not_exist" xml:space="preserve">
    <value>File does not exist</value>
  </data>
  <data name="no_permission_write" xml:space="preserve">
    <value>You don't have write permissions</value>
  </data>
  <data name="selected_structure_status" xml:space="preserve">
    <value>Selected structure status</value>
  </data>
  <data name="select_structure" xml:space="preserve">
    <value>Select a structure</value>
  </data>
  <data name="select_structure_first" xml:space="preserve">
    <value>Select a structure first</value>
  </data>
  <data name="select_structure_option" xml:space="preserve">
    <value>Select a structure</value>
  </data>
  <data name="service_inclusion_exclusion_blocked" xml:space="preserve">
    <value>Blocked</value>
  </data>
  <data name="service_inclusion_exlusion_blocked_status" xml:space="preserve">
    <value>Blocking Status</value>
  </data>
  <data name="structure_status_recap" xml:space="preserve">
    <value>Structure Status Summary</value>
  </data>
  <data name="service_inclusion_exclusion_name" xml:space="preserve">
    <value>Service Name</value>
  </data>
  <data name="service_blocked_status" xml:space="preserve">
    <value>Blocking Status</value>
  </data>
  <data name="structure_included" xml:space="preserve">
    <value>Structure Included</value>
  </data>
  <data name="structure_excluded" xml:space="preserve">
    <value>Structure Excluded</value>
  </data>
  <data name="structure_default_status" xml:space="preserve">
    <value>Default Status</value>
  </data>
  <data name="not_blocked" xml:space="preserve">
    <value>Not Blocked</value>
  </data>
  <data name="structure_selected" xml:space="preserve">
    <value>Selected Structure</value>
  </data>
  <data name="structure_removed_from_exclusion_prod" xml:space="preserve">
    <value>Structure {0} removed from exclusionsStructures.xml PROD</value>
  </data>
  <data name="structure_removed_from_inclusion_preprod" xml:space="preserve">
    <value> Structure {0} removed from inclusionsStructures.xml PREPROD</value>
  </data>
  <data name="structure_added_to_exclusion_prod" xml:space="preserve">
    <value>Structure {0} added to exclusionsStructures.xml PROD</value>
  </data>
  <data name="structure_added_to_inclusion_preprod" xml:space="preserve">
    <value>Structure {0} added to inclusionsStructures.xml PREPROD</value>
  </data>
  <data name="error_generic" xml:space="preserve">
    <value>Error: {0}Error: {0}</value>
  </data>
  <data name="structure_no_data_found" xml:space="preserve">
    <value> No structure was found in the database.</value>
  </data>
  <data name="structure_loading_error" xml:space="preserve">
    <value> Error while loading structures: {0}</value>
  </data>
  <data name="xml_loading_error_for_service" xml:space="preserve">
    <value>Error while loading XML files for service {0}: {1}</value>
  </data>
  <data name="structure_name" xml:space="preserve">
    <value> Structure name</value>
  </data>
  <data name="transfere-pointage-photo" xml:space="preserve">
    <value>Transfer photo clock-in</value>
  </data>
  <data name="transfert_pointagephoto_titre_page" xml:space="preserve">
    <value>Photo tagging {0}{1}</value>
  </data>
  <data name="transfert_pointagephoto_label_source" xml:space="preserve">
    <value>Source physical location (with image):</value>
  </data>
  <data name="transfert_pointagephoto_option_source_defaut" xml:space="preserve">
    <value>-- Select a source physical location --</value>
  </data>
  <data name="transfert_pointagephoto_label_cible" xml:space="preserve">
    <value>Target physical location:</value>
  </data>
  <data name="transfert_pointagephoto_option_cible_defaut" xml:space="preserve">
    <value> -- Select a target physical location --</value>
  </data>
  <data name="transfert_pointagephoto_bouton_transferer" xml:space="preserve">
    <value> Transfer</value>
  </data>
  <data name="transfert_pointagephoto_bouton_traitement" xml:space="preserve">
    <value> Processing...</value>
  </data>
  <data name="transfert_pointagephoto_bouton_retour" xml:space="preserve">
    <value>Back</value>
  </data>
  <data name="transfert_pointagephoto_label_chargement" xml:space="preserve">
    <value>Loading...</value>
  </data>
  <data name="transfert_pointagephoto_erreur_chargement" xml:space="preserve">
    <value>An error occurred while loading physical locations: {0}</value>
  </data>
  <data name="transfert_pointagephoto_erreur_source" xml:space="preserve">
    <value>Please select a source physical location.</value>
  </data>
  <data name="transfert_pointagephoto_erreur_cible" xml:space="preserve">
    <value>Please select a target physical location.</value>
  </data>
  <data name="transfert_pointagephoto_succes_transfert" xml:space="preserve">
    <value>Photo time tracking transfer was successful from physical location {0} to {1}.</value>
  </data>
  <data name="transfert_pointagephoto_echec_transfert" xml:space="preserve">
    <value>Transfer failed. Make sure the source location has an image.</value>
  </data>
  <data name="transfert_pointagephoto_exception_transfert" xml:space="preserve">
    <value> An error occurred during transfer: {0}</value>
  </data>
  <data name="transfert_pointagephoto_succes_chargement" xml:space="preserve">
    <value> Data loaded successfully</value>
  </data>
  <data name="button.generate_xml_file.label" xml:space="preserve">
    <value>Generate custom XML file</value>
  </data>
  <data name="button.generate_xml_file.loading" xml:space="preserve">
    <value>Generating custom XML file...</value>
  </data>
  <data name="xml_generation_environment_undefined" xml:space="preserve">
    <value>The environment is not defined in the configuration.</value>
  </data>
  <data name="xml_generation_success" xml:space="preserve">
    <value>XML file successfully generated.</value>
  </data>
  <data name="modal_format" xml:space="preserve">
    <value>Format</value>
  </data>
  <data name="xml_generation_error" xml:space="preserve">
    <value>Error while generating the XML file: {0}</value>
  </data>
  <data name="xml_generation_structureid_null" xml:space="preserve">
    <value>StructureId is null, generation is not possible.</value>
  </data>
  <data name="transfert_vignette_titre_section" xml:space="preserve">
    <value>Thumbnail Transfer</value>
  </data>
  <data name="transfert_vignette_label_source" xml:space="preserve">
    <value>Source Physical Location</value>
  </data>
  <data name="transfert_vignette_option_source_defaut" xml:space="preserve">
    <value>Select source physical location</value>
  </data>
  <data name="transfert_vignette_label_cible" xml:space="preserve">
    <value>Target Physical Location</value>
  </data>
  <data name="transfert_vignette_option_cible_defaut" xml:space="preserve">
    <value>Select target physical location</value>
  </data>
  <data name="transfert_vignette_bouton_transferer" xml:space="preserve">
    <value>Transfer Thumbnails</value>
  </data>
  <data name="transfert_vignette_bouton_traitement" xml:space="preserve">
    <value>Processing...</value>
  </data>
  <data name="transfert_pointagephoto_titre_section" xml:space="preserve">
    <value>Photo tagging transfer on (x,y)</value>
  </data>
  <data name="modal_format_help" xml:space="preserve">
    <value>Format help</value>
  </data>
  <data name="modal_example_code_help" xml:space="preserve">
    <value>Example code help</value>
  </data>
  <data name="couponstatus_active" xml:space="preserve">
    <value>Active</value>
  </data>
  <data name="couponstatus_expired" xml:space="preserve">
    <value>Expired</value>
  </data>
  <data name="couponstatus_used" xml:space="preserve">
    <value>Used</value>
  </data>
  <data name="couponstatus_undefined" xml:space="preserve">
    <value> Undefined</value>
  </data>
  <data name="search_off_no_translation_found_title" xml:space="preserve">
    <value>No translation found</value>
  </data>
  <data name="search_off_try_changing_search_criteria_message" xml:space="preserve">
    <value>Try changing your search criteria.</value>
  </data>
  <data name="search_off_missing_translation_notice" xml:space="preserve">
    <value>Missing translation</value>
  </data>
  <data name="voir_champs_vides_langue" xml:space="preserve">
    <value>View empty fields for the language</value>
  </data>
  <data name="modal.max_possible_number" xml:space="preserve">
    <value>Maximum possible number:</value>
  </data>
  <data name="modal.warning_too_many_coupons" xml:space="preserve">
    <value>The maximum number of possible coupons exceeds 2000. If you generate more than 2000 coupons, it will take longer.</value>
  </data>
  <data name="modal.confirm_generation_more_than_2000" xml:space="preserve">
    <value> You requested to generate more than 2000 coupons. Generation may take longer. Do you want to continue?</value>
  </data>
  <data name="toast.adjustment_done" xml:space="preserve">
    <value>Adjustment made</value>
  </data>
  <data name="toast.coupons_reduced_to_2000" xml:space="preserve">
    <value>The number of coupons has been reduced to 2000.</value>
  </data>
  <data name="toast.number_adjusted" xml:space="preserve">
    <value>Number adjusted</value>
  </data>
  <data name="toast.coupons_reduced_to_x" xml:space="preserve">
    <value>The number of coupons has been reduced to {0}.</value>
  </data>
  <data name="transfert_pointagephoto_bouton_verifier" xml:space="preserve">
    <value>Verify</value>
  </data>
  <data name="transfert_pointagephoto_bouton_generer_xml" xml:space="preserve">
    <value>Generate custom XML file</value>
  </data>
  <data name="transfert_resultats_incoherences_titre" xml:space="preserve">
    <value>Inconsistencies detected:</value>
  </data>
  <data name="transfert_resultats_source_unique" xml:space="preserve">
    <value>present in the source but not in the target</value>
  </data>
  <data name="transfert_resultats_cible_unique" xml:space="preserve">
    <value>present in the target but not in the source</value>
  </data>
  <data name="transfert_resultats_plus_elements" xml:space="preserve">
    <value>+ {0} more items not displayed.</value>
  </data>
  <data name="transfert_vignette_bouton_verifier" xml:space="preserve">
    <value> Verify</value>
  </data>
  <data name="transfert_vignette_bouton_generer_xml" xml:space="preserve">
    <value>Generate badge XML file</value>
  </data>
  <data name="transfert_resultats_identiques_vignette" xml:space="preserve">
    <value>Physical locations are identical.</value>
  </data>
  <data name="transfert_pointagephoto_verification_selection_lieux" xml:space="preserve">
    <value>Select a source and a target location to verify.</value>
  </data>
  <data name="transfert_pointagephoto_verification_incoherences_detectees" xml:space="preserve">
    <value> Inconsistencies have been detected.</value>
  </data>
  <data name="transfert_pointagephoto_verification_compatibles" xml:space="preserve">
    <value>Physical locations are compatible.</value>
  </data>
  <data name="transfert_pointagephoto_verification_erreur" xml:space="preserve">
    <value>Error during verification</value>
  </data>
  <data name="transfert_pointagephoto_transfert_incoherences_a_corriger" xml:space="preserve">
    <value>Fix the inconsistencies before transferring.</value>
  </data>
  <data name="transfert_pointagephoto_transfert_selection_lieux" xml:space="preserve">
    <value>Select a source and a target location.</value>
  </data>
  <data name="transfert_pointagephoto_export_structure_id_manquant" xml:space="preserve">
    <value>Structure ID missing.</value>
  </data>
  <data name="transfert_pointagephoto_export_environnement_indefini" xml:space="preserve">
    <value>Environment not defined.</value>
  </data>
  <data name="transfert_pointagephoto_export_xml_succes" xml:space="preserve">
    <value>XML file successfully generated.</value>
  </data>
  <data name="transfert_pointagephoto_export_xml_erreur" xml:space="preserve">
    <value>Error generating XML file: {0}</value>
  </data>
  <data name="transfert_pointagephoto_export_salle_structure_id_manquant" xml:space="preserve">
    <value>Structure ID missing.</value>
  </data>
  <data name="transfert_pointagephoto_export_salle_environnement_indefini" xml:space="preserve">
    <value> Environment not defined.</value>
  </data>
  <data name="transfert_pointagephoto_export_salle_xml_succes" xml:space="preserve">
    <value>XML file successfully generated.</value>
  </data>
  <data name="transfert_pointagephoto_export_salle_xml_erreur" xml:space="preserve">
    <value>Error generating XML file: {0}</value>
  </data>
  <data name="transfert_pointagephoto_incoherence_source" xml:space="preserve">
    <value>Rank {0}, seat {1} — present in the source but not in the target</value>
  </data>
  <data name="transfert_pointagephoto_incoherence_source_autres" xml:space="preserve">
    <value>+ {0} other items not displayed.</value>
  </data>
  <data name="transfert_pointagephoto_incoherence_cible" xml:space="preserve">
    <value>Rank {0}, seat {1} — present in the target but not in the source</value>
  </data>
  <data name="Rang {0}, siège {1} — présent dans la cible mais pas dans la source" xml:space="preserve">
    <value>+ {0} other items not displayed.</value>
  </data>
  <data name="transfert_pointagephoto_incoherence_cible_autres" xml:space="preserve">
    <value> + {0} other items not displayed.</value>
  </data>
  <data name="rolemanagement_edit_success_title" xml:space="preserve">
    <value>Edit successful</value>
  </data>
  <data name="rolemanagement_edit_success_message" xml:space="preserve">
    <value>The module was successfully edited.</value>
  </data>
  <data name="rolemanagement_edit_error_title" xml:space="preserve">
    <value> Error during editing</value>
  </data>
  <data name="rolemanagement_edit_error_message" xml:space="preserve">
    <value>An error occurred while editing: {0}</value>
  </data>
  <data name="rolemanagement_delete_success_title" xml:space="preserve">
    <value>Deletion successful</value>
  </data>
  <data name="rolemanagement_delete_success_messag" xml:space="preserve">
    <value>The module was successfully deleted.</value>
  </data>
  <data name="rolemanagement_delete_error_title" xml:space="preserve">
    <value> Error during deletion</value>
  </data>
  <data name="rolemanagement_delete_error_message" xml:space="preserve">
    <value>An error occurred while deleting: {0}</value>
  </data>
  <data name="rolemanagement_actions" xml:space="preserve">
    <value>Actions</value>
  </data>
  <data name="rolemanagement_edit" xml:space="preserve">
    <value>Edit module</value>
  </data>
  <data name="rolemanagement_delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="rolemanagement_module_name" xml:space="preserve">
    <value> Module name</value>
  </data>
  <data name="rolemanagement_module_comment" xml:space="preserve">
    <value>Module comment</value>
  </data>
  <data name="rolemanagement_save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="rolemanagement_cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="rolemanagement_edit_module" xml:space="preserve">
    <value> Edit module</value>
  </data>
  <data name="home_outil_gesion_tst" xml:space="preserve">
    <value>Quickly access your platform’s essential features</value>
  </data>
  <data name="home_btn_primary_acceder" xml:space="preserve">
    <value>Access</value>
  </data>
  <data name="home_worflow_questions" xml:space="preserve">
    <value>Ready to optimize your workflow?</value>
  </data>
  <data name="home_explorer_fonctionnalite" xml:space="preserve">
    <value>Explore the available features above to start boosting your productivity right now.</value>
  </data>
  <data name="cliquez_sur_les_cartes_pour_naviguer" xml:space="preserve">
    <value>Click on the cards to navigate</value>
  </data>
  <data name="stats_support_continu" xml:space="preserve">
    <value>Continuous Support</value>
  </data>
  <data name="stats_productivite" xml:space="preserve">
    <value> Productivity</value>
  </data>
  <data name="stats_optimise" xml:space="preserve">
    <value>Optimized</value>
  </data>
  <data name="stats_possibilites" xml:space="preserve">
    <value>Possibilities</value>
  </data>
  <data name="gestion-maquette-abo-fermer" xml:space="preserve">
    <value>Template Management (Abo)</value>
  </data>
  <data name="gestionmaquette_title" xml:space="preserve">
    <value>Template management for closed subscription</value>
  </data>
  <data name="gestionmaquette_loading" xml:space="preserve">
    <value>Loading...</value>
  </data>
  <data name="gestionmaquette_subscription_formule" xml:space="preserve">
    <value>Subscription Formula</value>
  </data>
  <data name="gestionmaquette_select_formula" xml:space="preserve">
    <value>Select a formula</value>
  </data>
  <data name="gestionmaquette_session" xml:space="preserve">
    <value>Session</value>
  </data>
  <data name="gestionmaquette_select_session" xml:space="preserve">
    <value>Select a session</value>
  </data>
  <data name="gestionmaquette_template" xml:space="preserve">
    <value>Template</value>
  </data>
  <data name="gestionmaquette_select_template" xml:space="preserve">
    <value>Select a template</value>
  </data>
  <data name="gestionmaquette_save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="gestionmaquette_existing_associations" xml:space="preserve">
    <value>Existing associations</value>
  </data>
  <data name="gestionmaquette_refresh" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="gestionmaquette_no_associations_found" xml:space="preserve">
    <value>No associations found</value>
  </data>
  <data name="gestionmaquette_formule" xml:space="preserve">
    <value>Formula</value>
  </data>
  <data name="gestionmaquette_operation_date" xml:space="preserve">
    <value>Operation date</value>
  </data>
  <data name="gestionmaquette_actions" xml:space="preserve">
    <value>Actions</value>
  </data>
  <data name="gestionmaquetteabofermer_message_error_loading_data" xml:space="preserve">
    <value>Error loading data</value>
  </data>
  <data name="gestionmaquetteabofermer_message_error_loading_associations" xml:space="preserve">
    <value>Error loading associations</value>
  </data>
  <data name="gestionmaquetteabofermer_message_please_select_all" xml:space="preserve">
    <value>Please select all items</value>
  </data>
  <data name="gestionmaquetteabofermer_message_association_already_exists" xml:space="preserve">
    <value>This association already exists</value>
  </data>
  <data name="gestionmaquetteabofermer_message_association_saved_successfully" xml:space="preserve">
    <value>Association saved successfully</value>
  </data>
  <data name="gestionmaquetteabofermer_message_error_saving_association" xml:space="preserve">
    <value>Error saving the association</value>
  </data>
  <data name="gestionmaquetteabofermer_message_association_deleted_successfully" xml:space="preserve">
    <value> Association deleted successfully</value>
  </data>
  <data name="gestionmaquetteabofermer_message_error_deleting_association" xml:space="preserve">
    <value>Error deleting the association</value>
  </data>
  <data name="gestionmaquetteabofermer_message_error" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="gestionmaquette_new_association" xml:space="preserve">
    <value>New link between layout, session, and subscription</value>
  </data>
  <data name="preparation-mise-vente" xml:space="preserve">
    <value> Prep for sale</value>
  </data>
  <data name="clean_tables_success_message" xml:space="preserve">
    <value>Tables were successfully cleaned. All queuing table data has been deleted.</value>
  </data>
  <data name="clean_tables_failed_message" xml:space="preserve">
    <value>Cleanup could not be performed. Recent data is still present.</value>
  </data>
  <data name="clean_tables_error_message" xml:space="preserve">
    <value>Error during cleanup: {0}</value>
  </data>
  <data name="check_cleanup_no_recent_data_message" xml:space="preserve">
    <value>No recent passage detected. Cleaning can be performed safely.</value>
  </data>
  <data name="check_cleanup_recent_data_warning" xml:space="preserve">
    <value>Warning: recent data (less than 1 hour) detected. Cleanup is not allowed for safety reasons.</value>
  </data>
  <data name="check_cleanup_error_message" xml:space="preserve">
    <value>Error during verification: {0}</value>
  </data>
  <data name="on_initialized_click_to_verify_message" xml:space="preserve">
    <value>Click on 'Check again' to verify the status of the passages before cleaning.</value>
  </data>
  <data name="preparation_mise_vente_title" xml:space="preserve">
    <value> Sale Preparation</value>
  </data>
  <data name="preparation_mise_vente_structure_label" xml:space="preserve">
    <value>Structure: {0} ({1})</value>
  </data>
  <data name="preparation_mise_vente_loading" xml:space="preserve">
    <value>Checking data in progress...</value>
  </data>
  <data name="preparation_mise_vente_success_message" xml:space="preserve">
    <value>Tables were successfully cleaned. All queuing table data has been deleted.</value>
  </data>
  <data name="preparation_mise_vente_error_message" xml:space="preserve">
    <value>Cleanup could not be performed. Recent data is present.</value>
  </data>
  <data name="preparation_mise_vente_check_error" xml:space="preserve">
    <value>Error during verification: {0}</value>
  </data>
  <data name="preparation_mise_vente_check_success" xml:space="preserve">
    <value>No recent passage detected. Cleaning can be performed safely.</value>
  </data>
  <data name="preparation_mise_vente_check_warning" xml:space="preserve">
    <value>Warning: Recent data (less than 1 hour) detected. Cleanup not possible.</value>
  </data>
  <data name="preparation_mise_vente_click_verify" xml:space="preserve">
    <value>Click on 'Check again' to verify the status of the passages before cleaning.</value>
  </data>
  <data name="preparation_mise_vente_verification_status" xml:space="preserve">
    <value>Verification Status</value>
  </data>
  <data name="preparation_mise_vente_not_verified" xml:space="preserve">
    <value>Not verified</value>
  </data>
  <data name="preparation_mise_vente_not_verified_description" xml:space="preserve">
    <value>The status of the passages has not been checked yet.</value>
  </data>
  <data name="preparation_mise_vente_not_verified_hint" xml:space="preserve">
    <value>Click 'Check again' to check the data.</value>
  </data>
  <data name="preparation_mise_vente_recent_data" xml:space="preserve">
    <value>Recent passages detected</value>
  </data>
  <data name="preparation_mise_vente_recent_data_description" xml:space="preserve">
    <value>Passages less than one hour old are present in the queuing table.</value>
  </data>
  <data name="preparation_mise_vente_recent_data_hint" xml:space="preserve">
    <value>Cleanup is currently blocked for safety reasons.</value>
  </data>
  <data name="preparation_mise_vente_ready_to_cleanup" xml:space="preserve">
    <value> Ready for Cleanup</value>
  </data>
  <data name="preparation_mise_vente_no_recent_data" xml:space="preserve">
    <value>No recent passage detected.</value>
  </data>
  <data name="preparation_mise_vente_safe_to_cleanup" xml:space="preserve">
    <value>Table cleanup can be safely performed.</value>
  </data>
  <data name="preparation_mise_vente_actions" xml:space="preserve">
    <value> Available Actions</value>
  </data>
  <data name="preparation_mise_vente_check_button" xml:space="preserve">
    <value> Check Again</value>
  </data>
  <data name="preparation_mise_vente_check_button_hin" xml:space="preserve">
    <value> Refreshes verification status</value>
  </data>
  <data name="preparation_mise_vente_cleanup_required" xml:space="preserve">
    <value>Verification Required</value>
  </data>
  <data name="preparation_mise_vente_cleanup_blocked" xml:space="preserve">
    <value>Cleanup Blocked</value>
  </data>
  <data name="preparation_mise_vente_cleanup_button" xml:space="preserve">
    <value>Clean the Tables</value>
  </data>
  <data name="preparation_mise_vente_cleanup_button_hint" xml:space="preserve">
    <value>Deletes all data from queuing tables</value>
  </data>
  <data name="preparation_mise_vente_cleanup_warning" xml:space="preserve">
    <value>Cleanup will delete all data from queuing_{0} and queuing_{0}_histopassages.</value>
  </data>
  <data name="preparation_mise_vente_cleanup_irreversible" xml:space="preserve">
    <value>Wait until the recent passages are more than one hour old or until the users are no longer connected.</value>
  </data>
  <data name="preparation_mise_vente_verify_first" xml:space="preserve">
    <value>Please check the status of the passages first before proceeding with the cleaning.</value>
  </data>
  <data name="preparation_mise_vente_verify_purpose" xml:space="preserve">
    <value>This check ensures no recent data will be lost.</value>
  </data>
  <data name="preparation_mise_vente_check_button_hint" xml:space="preserve">
    <value>Refreshes verification status</value>
  </data>
  <data name="preparation_mise_vente_cancel_button" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="preparation_mise_vente_cleanup_confirmation_title" xml:space="preserve">
    <value>Cleanup Confirmation</value>
  </data>
  <data name="preparation_mise_vente_advice_wait_or_disconnect" xml:space="preserve">
    <value>Wait until the recent passages are more than one hour old.</value>
  </data>
  <data name="tst_tool_configuration_name" xml:space="preserve">
    <value>Configuration</value>
  </data>
  <data name="tst_tool_configuration_description" xml:space="preserve">
    <value>Settings and configuration management</value>
  </data>
  <data name="tst_tool_commerce_name" xml:space="preserve">
    <value> Commercial management</value>
  </data>
  <data name="tst_tool_commerce_description" xml:space="preserve">
    <value>Commercial management tools</value>
  </data>
  <data name="tst_tool_roles_name" xml:space="preserve">
    <value>Roles management</value>
  </data>
  <data name="tst_tool_roles_description" xml:space="preserve">
    <value>Management of roles</value>
  </data>
  <data name="tst_tool_partners_name" xml:space="preserve">
    <value> Partner management</value>
  </data>
  <data name="tst_tool_partners_description" xml:space="preserve">
    <value>Partner management</value>
  </data>
  <data name="tst_tool_traductions_name" xml:space="preserve">
    <value>Translations</value>
  </data>
  <data name="tst_tool_traductions_description" xml:space="preserve">
    <value>Translation management</value>
  </data>
  <data name="tst_tool_monitoring_name" xml:space="preserve">
    <value>Monitoring</value>
  </data>
  <data name="tst_tool_monitoring_description" xml:space="preserve">
    <value>Monitoring and tracking</value>
  </data>
  <data name="tst_tool_securite_name" xml:space="preserve">
    <value>Security</value>
  </data>
  <data name="tst_tool_securite_description" xml:space="preserve">
    <value>Security management</value>
  </data>
  <data name="common_voir_modules_label" xml:space="preserve">
    <value>View modules</value>
  </data>
  <data name="common_survolez_outils_pour_voir_modules" xml:space="preserve">
    <value>Hover over the tools to see the available modules</value>
  </data>
  <data name="loading_spinner_visually_hidden_text" xml:space="preserve">
    <value>Loading...</value>
  </data>
  <data name="loading_spinner_message_text" xml:space="preserve">
    <value>Loading data...</value>
  </data>
  <data name="pagination_items_per_page_label" xml:space="preserve">
    <value>Items per page</value>
  </data>
  <data name="pagination_showing_items_range" xml:space="preserve">
    <value>Showing items {0} to {1} of {2}</value>
  </data>
  <data name="pagination_navigation_label" xml:space="preserve">
    <value>Pagination navigation</value>
  </data>
  <data name="pagination_button_previous" xml:space="preserve">
    <value>Previous</value>
  </data>
  <data name="pagination_button_next" xml:space="preserve">
    <value> Next</value>
  </data>
  <data name="pagination_page_info_text" xml:space="preserve">
    <value>Page {0} of {1}</value>
  </data>
  <data name="toast_message_data_loaded_success" xml:space="preserve">
    <value>Data loaded successfully</value>
  </data>
  <data name="widget-cross-selling" xml:space="preserve">
    <value>Cross-selling</value>
  </data>
  <data name="widget-waiting-list" xml:space="preserve">
    <value>Queue config.</value>
  </data>
  <data name="widget-catalogue-offre" xml:space="preserve">
    <value>Catalogs of Offers</value>
  </data>
  <data name="liste_catalogues_offres" xml:space="preserve">
    <value> Offer Catalogs</value>
  </data>
  <data name="configuration_listes_attente" xml:space="preserve">
    <value> Waiting List Configuration</value>
  </data>
  <data name="gestion_widgets" xml:space="preserve">
    <value>Widget Management</value>
  </data>
  <data name="description_gestion_widgets" xml:space="preserve">
    <value> Manages widget display and order.</value>
  </data>
  <data name=" liste-structure-with-partenaires" xml:space="preserve">
    <value>List of structures and their partners</value>
  </data>
  <data name="liaison_revendeur_title" xml:space="preserve">
    <value>Reseller-Buyer Profile Link</value>
  </data>
  <data name="liaison_revendeur_description" xml:space="preserve">
    <value>Create links between resellers and buyer profiles on structures</value>
  </data>
  <data name="loading" xml:space="preserve">
    <value>Loading...</value>
  </data>
  <data name="loading_data" xml:space="preserve">
    <value>Loading data...</value>
  </data>
  <data name="step_1_select_revendeur" xml:space="preserve">
    <value>Step 1: Select a Reseller</value>
  </data>
  <data name="step_2_select_profil_acheteur" xml:space="preserve">
    <value>Step 2: Select a Buyer Profile</value>
  </data>
  <data name="step_3_select_structure" xml:space="preserve">
    <value>Step 3: Select a Structure</value>
  </data>
  <data name="select" xml:space="preserve">
    <value>Select</value>
  </data>
  <data name="no_revendeurs_found" xml:space="preserve">
    <value>No resellers found</value>
  </data>
  <data name="selected_revendeur" xml:space="preserve">
    <value>Selected reseller</value>
  </data>
  <data name="back" xml:space="preserve">
    <value>Back</value>
  </data>
  <data name="no_profils_lies_found" xml:space="preserve">
    <value>No linked profiles found</value>
  </data>
  <data name="show_all_profils" xml:space="preserve">
    <value>Show all profiles</value>
  </data>
  <data name="selected_profil" xml:space="preserve">
    <value>Selected profile</value>
  </data>
  <data name="choose_structure" xml:space="preserve">
    <value>Choose structure</value>
  </data>
  <data name="select_structure" xml:space="preserve">
    <value>Select a structure</value>
  </data>
  <data name="saving" xml:space="preserve">
    <value>Saving...</value>
  </data>
  <data name="save_liaison" xml:space="preserve">
    <value>Save Link</value>
  </data>
  <data name="liaison_saved_successfully" xml:space="preserve">
    <value>Link saved successfully!</value>
  </data>
  <data name="liaison_details" xml:space="preserve">
    <value>Link details</value>
  </data>
  <data name="revendeur" xml:space="preserve">
    <value>Reseller</value>
  </data>
  <data name="profil_acheteur" xml:space="preserve">
    <value>Buyer Profile</value>
  </data>
  <data name="structure" xml:space="preserve">
    <value>Structure</value>
  </data>
  <data name="create_new_liaison" xml:space="preserve">
    <value>Create New Link</value>
  </data>
  <data name="no_profils_lies_found" xml:space="preserve">
    <value>No linked profiles found for this reseller</value>
  </data>
  <data name="no_profils_lies_explanation" xml:space="preserve">
    <value>This reseller has no buyer profiles linked yet. You can create a new link by selecting a buyer profile from the structure.</value>
  </data>
  <data name="liaison_revendeur_description_new_workflow" xml:space="preserve">
    <value>Create links between resellers and buyer profiles: 1) Select a reseller, 2) Choose their buyer profile, 3) Select target structure and its buyer profile</value>
  </data>
  <data name="select_revendeur" xml:space="preserve">
    <value>Select a reseller</value>
  </data>
  <data name="select_profil_acheteur" xml:space="preserve">
    <value>Select a buyer profile</value>
  </data>
  <data name="select_structure_and_profil" xml:space="preserve">
    <value>Select structure and profile</value>
  </data>
  <data name="confirmation" xml:space="preserve">
    <value>Confirmation</value>
  </data>
  <data name="select_revendeur_description" xml:space="preserve">
    <value>Choose the reseller for which you want to create a link</value>
  </data>
  <data name="revendeur_from_structure" xml:space="preserve">
    <value>Reseller from structure</value>
  </data>
  <data name="step_3_select_structure_and_profil" xml:space="preserve">
    <value>Step 3: Select target structure and buyer profile</value>
  </data>
  <data name="select_target_structure_explanation" xml:space="preserve">
    <value>Select the target structure where you want to create the link, then choose the buyer profile of that structure</value>
  </data>
  <data name="select_target_structure" xml:space="preserve">
    <value>Select target structure</value>
  </data>
  <data name="no_revendeurs_database_issue" xml:space="preserve">
    <value>No resellers found. Check database connections for structures.</value>
  </data>
  <data name="revendeur_profil_acheteur" xml:space="preserve">
    <value>Reseller buyer profile</value>
  </data>
  <data name="target_structure" xml:space="preserve">
    <value>Target structure</value>
  </data>
  <data name="structure_profil_acheteur" xml:space="preserve">
    <value>Structure buyer profile</value>
  </data>
  <data name="select_structure_profil" xml:space="preserve">
    <value>Select structure profile</value>
  </data>
  <data name="step_3_select_structure_profil" xml:space="preserve">
    <value>Step 3: Select structure buyer profile</value>
  </data>
  <data name="select_structure_profil_explanation" xml:space="preserve">
    <value>Select the buyer profile from the reseller's structure to associate</value>
  </data>
  <data name="revendeur_structure" xml:space="preserve">
    <value>Reseller structure</value>
  </data>
</root>