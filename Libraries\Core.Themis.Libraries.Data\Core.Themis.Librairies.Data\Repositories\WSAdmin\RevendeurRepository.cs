using AutoMapper;
using Core.Themis.Libraries.Data.Context.Interfaces;
using Core.Themis.Libraries.Data.Entities.WSAdmin;
using Core.Themis.Libraries.Data.Repositories.Common;
using Core.Themis.Libraries.Data.Repositories.WSAdmin.Interfaces;
using Core.Themis.Libraries.Utilities.Enums;
using Dapper;
using Microsoft.Extensions.Caching.Memory;
using System.Data.SqlClient;

namespace Core.Themis.Libraries.Data.Repositories.WSAdmin
{
    public class RevendeurRepository : GenericRepository<RevendeurEntity>, IRevendeurRepository
    {
        private readonly IDbContext _dbContext;

        public RevendeurRepository(IDbContext dbContext, IMemoryCache memoryCache, IMapper mapper)
            : base(ContextType.WSAdmin, dbContext, memoryCache, mapper)
        {
            _dbContext = dbContext;
        }

        /// <summary>
        /// Récupère tous les revendeurs
        /// </summary>
        /// <returns>Liste des revendeurs</returns>
        public async Task<IEnumerable<RevendeurEntity>> GetAllRevendeursAsync()
        {
            try
            {
                using SqlConnection connection = _dbContext.GetWsAdminConnection();
                connection.Open();

                string sqlQuery = @"SELECT id, name FROM revendeurs ORDER BY name";

                return await connection.QueryAsync<RevendeurEntity>(sqlQuery);
            }
            catch (System.Data.SqlClient.SqlException)
            {
                // Si la table n'existe pas dans WSAdmin, retourner des données statiques
                return new List<RevendeurEntity>
                {
                    new RevendeurEntity { Id = 1, Name = "TICKETAC" },
                    new RevendeurEntity { Id = 2, Name = "BILLETREDUC" },
                    new RevendeurEntity { Id = 4, Name = "VENTE_PRIVEE" }
                };
            }
        }
    }
}
