using AutoMapper;
using Core.Themis.Libraries.Data.Context.Interfaces;
using Core.Themis.Libraries.Data.Entities.WSAdmin;
using Core.Themis.Libraries.Data.Repositories.Common;
using Core.Themis.Libraries.Data.Repositories.WSAdmin.Interfaces;
using Core.Themis.Libraries.Utilities.Enums;
using Dapper;
using Microsoft.Extensions.Caching.Memory;
using System.Data.SqlClient;

namespace Core.Themis.Libraries.Data.Repositories.WSAdmin
{
    public class RevendeurRepository : GenericRepository<RevendeurEntity>, IRevendeurRepository
    {
        private readonly IDbContext _dbContext;

        public RevendeurRepository(IDbContext dbContext, IMemoryCache memoryCache, IMapper mapper)
            : base(ContextType.WSAdmin, dbContext, memoryCache, mapper)
        {
            _dbContext = dbContext;
        }

        /// <summary>
        /// Récupère tous les revendeurs
        /// </summary>
        /// <returns>Liste des revendeurs</returns>
        public async Task<IEnumerable<RevendeurEntity>> GetAllRevendeursAsync()
        {
            // Essayer d'abord avec WSAdmin
            try
            {
                using SqlConnection connection = _dbContext.GetWsAdminConnection();
                connection.Open();

                string sqlQuery = @"SELECT id, name FROM revendeurs ORDER BY name";

                return await connection.QueryAsync<RevendeurEntity>(sqlQuery);
            }
            catch (System.Data.SqlClient.SqlException ex)
            {
                Console.WriteLine($"Erreur WSAdmin: {ex.Message}");

                // Essayer avec une structure spécifique (194 par exemple)
                try
                {
                    using SqlConnection connection = _dbContext.GetStructureConnection("0194");
                    connection.Open();

                    string sqlQuery = @"SELECT id, name FROM revendeurs ORDER BY name";

                    return await connection.QueryAsync<RevendeurEntity>(sqlQuery);
                }
                catch (System.Data.SqlClient.SqlException ex2)
                {
                    Console.WriteLine($"Erreur Structure 194: {ex2.Message}");

                    // Retourner une liste vide si aucune connexion ne fonctionne
                    return new List<RevendeurEntity>();
                }
            }
        }
    }
}
