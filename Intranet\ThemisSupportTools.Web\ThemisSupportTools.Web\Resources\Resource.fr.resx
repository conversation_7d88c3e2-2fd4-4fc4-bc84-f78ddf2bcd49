﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="about" xml:space="preserve">
    <value>À propos</value>
  </data>
  <data name="dropdown_filter_groupKey_fieldName" xml:space="preserve">
    <value>Filtre Groupe de clés</value>
  </data>
  <data name="search_by_key_fieldName" xml:space="preserve">
    <value> Recherche par clés</value>
  </data>
  <data name="loading_spinner_span" xml:space="preserve">
    <value>Chargement en cours...</value>
  </data>
  <data name="show_mandatory_fields_label" xml:space="preserve">
    <value>Afficher les champs obligatoires</value>
  </data>
  <data name="show_modified_fields_label" xml:space="preserve">
    <value>Afficher les champs modifiés</value>
  </data>
  <data name="restore_last_file_button_btnSecondary" xml:space="preserve">
    <value>Restaurer le dernier fichier</value>
  </data>
  <data name="change_structure_button_btnDanger" xml:space="preserve">
    <value>Changer de structure</value>
  </data>
  <data name="save_button" xml:space="preserve">
    <value>Sauvegarder</value>
  </data>
  <data name="confirm_switch_to_new_structure" xml:space="preserve">
    <value>Etes-vous sûr de vouloir changer de structure ?</value>
  </data>
  <data name="changeStructure_button_yes" xml:space="preserve">
    <value>CHANGER DE STRUCTURE</value>
  </data>
  <data name="cancel_button_no" xml:space="preserve">
    <value>ANNULER</value>
  </data>
  <data name="restore_action" xml:space="preserve">
    <value>Restaurer</value>
  </data>
  <data name="label_create_section" xml:space="preserve">
    <value>Créer une section</value>
  </data>
  <data name="confirm_restore_last_saved_file" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir restaurer le dernier fichier sauvegardé ?</value>
  </data>
  <data name="search_key_input_placeholder" xml:space="preserve">
    <value>Renseignez une clé...</value>
  </data>
  <data name="dropdown_choose_section" xml:space="preserve">
    <value>Choisir une Section...</value>
  </data>
  <data name="choose_structure" xml:space="preserve">
    <value>Choisir une structure</value>
  </data>
  <data name="confirm_dialog_yes_button_text" xml:space="preserve">
    <value>J'ai compris</value>
  </data>
  <data name="confirm_dialog_message_time_expired" xml:space="preserve">
    <value>Le temps est écoulé. Nous allons vous rediriger vers la page d'accueil</value>
  </data>
  <data name="toast_message_title_modifications_in_progress" xml:space="preserve">
    <value>Modifications en cours</value>
  </data>
  <data name="toast_message_no_changes_in_form" xml:space="preserve">
    <value>Aucune modification sur ce formulaire !</value>
  </data>
  <data name="toast_message_title_form_validation" xml:space="preserve">
    <value>Validation du formulaire</value>
  </data>
  <data name="toast_message_empty_required_fields" xml:space="preserve">
    <value>Il reste des champs obligatoires vides</value>
  </data>
  <data name="confirm_save_changes_message" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir sauvegarder les modifications ?</value>
  </data>
  <data name="popup_save_success_message" xml:space="preserve">
    <value>Sauvegarde modifications avec succès, Well Done !</value>
  </data>
  <data name="popup_save_success_button" xml:space="preserve">
    <value>Retour à l'accueil</value>
  </data>
  <data name="file_in_use_message" xml:space="preserve">
    <value>Le fichier est en cours d'utilisation par {0} sur la structure N° {1}, merci de changer de structure !</value>
  </data>
  <data name="create-new-structure" xml:space="preserve">
    <value>Créer une nouvelle structure</value>
  </data>
  <data name="config-ini" xml:space="preserve">
    <value>Gérer le fichier Config.ini</value>
  </data>
  <data name="appsettings-plateforms" xml:space="preserve">
    <value>Paramètres des plateformes</value>
  </data>
  <data name="partners" xml:space="preserve">
    <value>Gestion des partenaires</value>
  </data>
  <data name="translations-terms" xml:space="preserve">
    <value>Gestion des traductions</value>
  </data>
  <data name="button_edit" xml:space="preserve">
    <value>Modifier</value>
  </data>
  <data name="button_delete" xml:space="preserve">
    <value>Supprimer</value>
  </data>
  <data name="select_all_sections" xml:space="preserve">
    <value>Toutes les sections</value>
  </data>
  <data name="select_view_empty_fields_language" xml:space="preserve">
    <value>Voir les champs vides de la langue</value>
  </data>
  <data name="button_filter" xml:space="preserve">
    <value>Filtrer</value>
  </data>
  <data name="icon_search" xml:space="preserve">
    <value>Chercher</value>
  </data>
  <data name="button_new_translation" xml:space="preserve">
    <value>Nouvelle traduction</value>
  </data>
  <data name="label_translation_count" xml:space="preserve">
    <value>{0} traduction(s)</value>
  </data>
  <data name="dialog_delete_message" xml:space="preserve">
    <value>Etes-vous sûr de vouloir supprimer la traduction {0} ?</value>
  </data>
  <data name="button_cancel" xml:space="preserve">
    <value>Annuler</value>
  </data>
  <data name="translations_title" xml:space="preserve">
    <value>Traductions</value>
  </data>
  <data name="new_partner" xml:space="preserve">
    <value>Nouveau Partenaire</value>
  </data>
  <data name="search_button" xml:space="preserve">
    <value>Rechercher</value>
  </data>
  <data name="confirm_delete_partner" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir supprimer ce partenaire &lt;strong&gt;{0}&lt;/strong&gt;?</value>
  </data>
  <data name="confirm_button" xml:space="preserve">
    <value>Confirmer</value>
  </data>
  <data name="choose_partner" xml:space="preserve">
    <value>Choisir un partenaire</value>
  </data>
  <data name="new_secret_key" xml:space="preserve">
    <value>Nouvelle clé secrète</value>
  </data>
  <data name="button_validate" xml:space="preserve">
    <value>Valider</value>
  </data>
  <data name="title_parameters" xml:space="preserve">
    <value>Paramètres</value>
  </data>
  <data name="label_name_partener" xml:space="preserve">
    <value>Nom</value>
  </data>
  <data name="partener_secret_key" xml:space="preserve">
    <value>Clé secrète</value>
  </data>
  <data name="label_partener_structure" xml:space="preserve">
    <value>Structures</value>
  </data>
  <data name="label_partener_roles" xml:space="preserve">
    <value>Rôles </value>
  </data>
  <data name="title_edit_section" xml:space="preserve">
    <value> Éditer une section</value>
  </data>
  <data name="label_key_section" xml:space="preserve">
    <value>Clé de la section</value>
  </data>
  <data name="label_description_section" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="h1_sections" xml:space="preserve">
    <value>Sections</value>
  </data>
  <data name="search_by_keyword" xml:space="preserve">
    <value>Rechercher par mot-clé</value>
  </data>
  <data name="new_section" xml:space="preserve">
    <value>Nouvelle section</value>
  </data>
  <data name="confirmation_delete_section" xml:space="preserve">
    <value> Êtes-vous sûr de vouloir supprimer la section</value>
  </data>
  <data name="new_variable" xml:space="preserve">
    <value>Nouvelle variable</value>
  </data>
  <data name="label_variable_name" xml:space="preserve">
    <value>Nom de la variable</value>
  </data>
  <data name="label_description_variable" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="self_closing_tag" xml:space="preserve">
    <value>Balise auto fermante</value>
  </data>
  <data name="edit_variable" xml:space="preserve">
    <value>Éditer une variable</value>
  </data>
  <data name="h1_variables" xml:space="preserve">
    <value>Variables</value>
  </data>
  <data name="confirm_delete_variable" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir supprimer cette variable ?</value>
  </data>
  <data name="remaining_time" xml:space="preserve">
    <value>Temp restant</value>
  </data>
  <data name="new_translation" xml:space="preserve">
    <value>Nouvelle traduction</value>
  </data>
  <data name="section_name" xml:space="preserve">
    <value>Nom de la section</value>
  </data>
  <data name="select_section" xml:space="preserve">
    <value>Sélectionnez une section</value>
  </data>
  <data name="translation_key" xml:space="preserve">
    <value>Clé de la traduction</value>
  </data>
  <data name="parent_translation_key" xml:space="preserve">
    <value>Clé de la traduction parente</value>
  </data>
  <data name="select_parent_key" xml:space="preserve">
    <value>Sélectionnez une clé parente</value>
  </data>
  <data name="linked_to_existing_key" xml:space="preserve">
    <value>Relié à une clé existante</value>
  </data>
  <data name="visible_only_for_admin_status" xml:space="preserve">
    <value>Visible uniquement pour le statut Admin</value>
  </data>
  <data name="edit_translation" xml:space="preserve">
    <value>Éditer une traduction</value>
  </data>
  <data name="toast_message_title_deletion_success" xml:space="preserve">
    <value>Suppression effectuée</value>
  </data>
  <data name="toast_message_deletion_successful" xml:space="preserve">
    <value>La suppression a été effectuée avec succès.</value>
  </data>
  <data name="error_partner_exists" xml:space="preserve">
    <value>Le partenaire '{0}' existe déjà.</value>
  </data>
  <data name="select_language" xml:space="preserve">
    <value>Sélectionnez une langue</value>
  </data>
  <data name="label_translation_indiv_platform" xml:space="preserve">
    <value>Gérer les traductions de la plateforme INDIV</value>
  </data>
  <data name="label_translation_customer_platform" xml:space="preserve">
    <value>Gérer les traductions de la plateforme CUSTOMER</value>
  </data>
  <data name="label_translation_abo_platform" xml:space="preserve">
    <value>Gérer les traductions de la plateforme ABO</value>
  </data>
  <data name="button_translations_indiv_platform" xml:space="preserve">
    <value>Traductions de la plateforme INDIV</value>
  </data>
  <data name="button_translations_customer_platform" xml:space="preserve">
    <value>Traductions de l'espace Client</value>
  </data>
  <data name="button_translations_abo_platform" xml:space="preserve">
    <value>Traductions de la plateforme Abonnement</value>
  </data>
  <data name="error_exist_partener_title" xml:space="preserve">
    <value>Erreur</value>
  </data>
  <data name="delete_error_title" xml:space="preserve">
    <value>Erreur de suppression</value>
  </data>
  <data name="delete_errorm_message" xml:space="preserve">
    <value>Une erreur est survenue lors de la suppression de la clé de traduction {0}.</value>
  </data>
  <data name="delete_success_title" xml:space="preserve">
    <value>Suppression réussie</value>
  </data>
  <data name="delete_success_message" xml:space="preserve">
    <value>La clé de traduction {0} a été supprimée avec succès.</value>
  </data>
  <data name="partners_count" xml:space="preserve">
    <value>partenaire(s)</value>
  </data>
  <data name="no_partners_associated" xml:space="preserve">
    <value>Aucun partenaire associé</value>
  </data>
  <data name="choose_structure" xml:space="preserve">
    <value>Choisir une Structure</value>
  </data>
  <data name="select_structure" xml:space="preserve">
    <value>Sélectionner une Structure</value>
  </data>
  <data name="select_structure_placeholder" xml:space="preserve">
    <value>-- Sélectionnez une structure --</value>
  </data>
  <data name="search_partners_in_structure" xml:space="preserve">
    <value>Rechercher des partenaires dans cette structure</value>
  </data>
  <data name="search_partner_placeholder" xml:space="preserve">
    <value>Saisir le nom du partenaire...</value>
  </data>
  <data name="partners_of_structure" xml:space="preserve">
    <value>Partenaires de</value>
  </data>
  <data name="partners_found" xml:space="preserve">
    <value>partenaire(s) trouvé(s)</value>
  </data>
  <data name="linked_structures" xml:space="preserve">
    <value>structure(s) liée(s)</value>
  </data>
  <data name="no_partners_found_search" xml:space="preserve">
    <value>Aucun partenaire trouvé correspondant à votre recherche</value>
  </data>
  <data name="no_partners_in_structure" xml:space="preserve">
    <value>Cette structure n'a aucun partenaire associé</value>
  </data>
  <data name="select_structure_to_view_partners" xml:space="preserve">
    <value>Sélectionnez une structure ci-dessus pour voir ses partenaires</value>
  </data>
  <data name="toast_save_success_message" xml:space="preserve">
    <value>Enregistrement réussi</value>
  </data>
  <data name="toast_message_title_success_edit" xml:space="preserve">
    <value>Succès de la modification</value>
  </data>
  <data name="toast_message_success_edit_partner_saved" xml:space="preserve">
    <value>Les modifications du partenaire ont été sauvegardées avec succès</value>
  </data>
  <data name="message_champ_obligatoire_xml" xml:space="preserve">
    <value> Ce champ est obligatoire</value>
  </data>
  <data name="variables_comment_montant_gratuit_xml" xml:space="preserve">
    <value>Trois cas valeur du montant 0:0, 1: gratuit, 2: non inclus</value>
  </data>
  <data name="variables_comment_xml" xml:space="preserve">
    <value>Valeurs comprises de 5 à 7</value>
  </data>
  <data name="variables_comment_type_alpha_xml" xml:space="preserve">
    <value>Alphanumérique</value>
  </data>
  <data name="paiement_comment_operateur_creation_commande_xml" xml:space="preserve">
    <value>Opérateur utilisé pour la création de la commande</value>
  </data>
  <data name="paiement_comment_web_operateur_id_xml" xml:space="preserve">
    <value>ID de l'opérateur web</value>
  </data>
  <data name="paiement_comment_web_post_id_xml" xml:space="preserve">
    <value>ID du poste web</value>
  </data>
  <data name="paiement_comment_currency_xml" xml:space="preserve">
    <value>Devise utilisée dans la transaction</value>
  </data>
  <data name="paiement_comment_activation_paiement_multiple_xml" xml:space="preserve">
    <value>Activation du paiement en plusieurs fois </value>
  </data>
  <data name="paiement_comment_capture_day_xml" xml:space="preserve">
    <value>Jour de capture du paiement</value>
  </data>
  <data name="paiement_comment_periode_xml" xml:space="preserve">
    <value>Période pour le paiement</value>
  </data>
  <data name="paiement_comment_montant_min_nb_payment_xml" xml:space="preserve">
    <value>Montant minimum pour le nombre de paiements</value>
  </data>
  <data name="paiement_comment_filieren_payment_xml" xml:space="preserve">
    <value>Filère du paiement</value>
  </data>
  <data name="paiement_comment_url_retour_err_xml" xml:space="preserve">
    <value>URL de retour en cas d'erreur</value>
  </data>
  <data name="paiement_comment_url_retour_vente_xml" xml:space="preserve">
    <value>URL de retour après une vente</value>
  </data>
  <data name="paiement_comment_url_retour_err_vente_xml" xml:space="preserve">
    <value>URL de retour en cas d'erreur lors de la vente</value>
  </data>
  <data name="paiement_comment_url_retourannul_vente_xml" xml:space="preserve">
    <value>URL de retour pour une vente annulée</value>
  </data>
  <data name="paiement_comment_url_retour_groupe_xml" xml:space="preserve">
    <value>URL de retour pour un groupe</value>
  </data>
  <data name="paiement_comment_url_retour_err_groupe_xml" xml:space="preserve">
    <value>Rückgabe-URL bei Fehler für eine Gruppe</value>
  </data>
  <data name="paiement_comment_url_retourannul_groupe_xml" xml:space="preserve">
    <value> URL de retour pour un groupe annulé</value>
  </data>
  <data name="paiement_comment_nb_payment_xml" xml:space="preserve">
    <value>Nombre de paiements autorisés</value>
  </data>
  <data name="paiement_comment_doe_edition_xml" xml:space="preserve">
    <value> Activation de l'édition des documents</value>
  </data>
  <data name="paiement_comment_activation_asynchrone_xml" xml:space="preserve">
    <value>Activation du traitement asynchrone</value>
  </data>
  <data name="paiement_comment_not_asynchrone_pa_xml" xml:space="preserve">
    <value>Non-synchrone pour le profil d'acheteur</value>
  </data>
  <data name="paiement_comment_urls_site_xml" xml:space="preserve">
    <value>URL du site pour le paiemen</value>
  </data>
  <data name="paiement_comment_version_xml" xml:space="preserve">
    <value>Version de l'application de paiement</value>
  </data>
  <data name="paiement_comment_activation_template_mail_xml" xml:space="preserve">
    <value>Activation du modèle d'email</value>
  </data>
  <data name="paiement_comment_desactiver_envoi_piece_jointe_pdf_xml" xml:space="preserve">
    <value>Désactivation de l'envoi de la pièce jointe (PDF)</value>
  </data>
  <data name="paiement_comment_id_marchand_spplus_xml" xml:space="preserve">
    <value>Identifiant marchand SP+</value>
  </data>
  <data name="paiement_comment_url_retour_abo_xml" xml:space="preserve">
    <value>URL de retour pour l'abonnement</value>
  </data>
  <data name="paiement_comment_url_retour_err_abo_xml" xml:space="preserve">
    <value>Rückgabe-URL bei Fehler für das Abonnement</value>
  </data>
  <data name="paiement_comment_url_retourannul_abo_xml" xml:space="preserve">
    <value>URL de retour pour annulation d'abonnement</value>
  </data>
  <data name="param_langue_com_configini_xml" xml:space="preserve">
    <value>Paramètre de langue de la configuration</value>
  </data>
  <data name="param_comment_devise_code_xml" xml:space="preserve">
    <value>Code de devise</value>
  </data>
  <data name="param_comment_devise_iso_xml" xml:space="preserve">
    <value>Code ISO de la devise</value>
  </data>
  <data name="param_comment_devise_before_xml" xml:space="preserve">
    <value>Position de la devise (avant ou après le montant)</value>
  </data>
  <data name="param_comment_devise_separator_xml" xml:space="preserve">
    <value>Séparateur de la devise</value>
  </data>
  <data name="param_comment_prestataire_paiement_xml" xml:space="preserve">
    <value> Prestataire de paiement</value>
  </data>
  <data name="param_comment_bonscadeaux_xml" xml:space="preserve">
    <value>Bons cadeaux disponibles</value>
  </data>
  <data name="param_comment_bannername_xml" xml:space="preserve">
    <value>Nom de la bannière</value>
  </data>
  <data name="param_comment_extensionbanner_xml" xml:space="preserve">
    <value>Extension de la bannière (on prend le premier trouvé)</value>
  </data>
  <data name="param_comment_insurance_xml" xml:space="preserve">
    <value> Gérer le contrat d'assurance, oui=1</value>
  </data>
  <data name="param_comment_minutestogotopaiement_xml" xml:space="preserve">
    <value>Temps avant le paiement (en minutes)</value>
  </data>
  <data name="param_comment_fraisdossiermontantgratuit_xml" xml:space="preserve">
    <value>Frais de dossier pour montant gratuit</value>
  </data>
  <data name="param_comment_groupe_manif_exclude_widget_xml" xml:space="preserve">
    <value>Groupe de manifestation exclu des widgets</value>
  </data>
  <data name="param_comment_multilangue_xml" xml:space="preserve">
    <value>Activation du multilinguisme</value>
  </data>
  <data name="param_comment_langue_en_xml" xml:space="preserve">
    <value>Langue anglaise</value>
  </data>
  <data name="param_comment_langue_it_xml" xml:space="preserve">
    <value>Langue italienne</value>
  </data>
  <data name="param_comment_langue_sp_xml" xml:space="preserve">
    <value>Langue espagnole</value>
  </data>
  <data name="param_comment_langue_de_xml" xml:space="preserve">
    <value>Langue allemande</value>
  </data>
  <data name="param_comment_showonlyonedate_xml" xml:space="preserve">
    <value>Afficher une seule date</value>
  </data>
  <data name="param_comment_usecustomerarea_xml" xml:space="preserve">
    <value>Utiliser la zone  client</value>
  </data>
  <data name="facture_mode_comment_facturemode_id_xml" xml:space="preserve">
    <value>Mode de paiement facture</value>
  </data>
  <data name="param_comment_facturemode_seuildeblocage_xml" xml:space="preserve">
    <value>Seuil de blocage</value>
  </data>
  <data name="comment_smtpclient_ip_xml" xml:space="preserve">
    <value>IP du serveur mail</value>
  </data>
  <data name="comment_navigation_accesfchoixseance_xml " xml:space="preserve">
    <value> Groupe de clé Obsolète</value>
  </data>
  <data name="comment_navigation_panier_xml" xml:space="preserve">
    <value>Groupe de clé Obsolète</value>
  </data>
  <data name="comment_priseplacessurplan_showunavailableseats_xml" xml:space="preserve">
    <value>Afficher les sièges non disponibles</value>
  </data>
  <data name="comment_priseplacessurplan_startimagesfauteuilsx_xml" xml:space="preserve">
    <value> Dimension minimale des fauteuils X</value>
  </data>
  <data name="comment_priseplacessurplan_startimagesfauteuilsy_xml" xml:space="preserve">
    <value>Dimension minimale des fauteuils Y</value>
  </data>
  <data name="comment_marker_printathome_xml" xml:space="preserve">
    <value>Affiche un texte spécifique si print@home choisi</value>
  </data>
  <data name="comment_marker_controlprice_xml" xml:space="preserve">
    <value>ID des tarifs qui affichent un texte spécifique</value>
  </data>
  <data name="comment_email_senderadresse_xml" xml:space="preserve">
    <value> Adresse expéditrice pour envoi email confirmation d’achat</value>
  </data>
  <data name="comment_email_replyadresse_xml" xml:space="preserve">
    <value>Adresse utilisée si réponse</value>
  </data>
  <data name="comment_email_copyadresse_xml" xml:space="preserve">
    <value>Adresse pour copie de l’email confirmation d’achat</value>
  </data>
  <data name="comment_email_blindcopyadresse_xml" xml:space="preserve">
    <value>Adresse en copie cachée</value>
  </data>
  <data name="emailerror_comment_senderadresse_xml" xml:space="preserve">
    <value>Adresse expéditrice</value>
  </data>
  <data name="emailerror_comment_replyadresse_xml" xml:space="preserve">
    <value>Adresse utilisée si réponse</value>
  </data>
  <data name="emailerror_comment_copyadresse_xml" xml:space="preserve">
    <value> Adresse pour copie</value>
  </data>
  <data name="emailerror_comment_blindcopyadresse_xml" xml:space="preserve">
    <value>Adresse en copie cachée</value>
  </data>
  <data name="emailerror_comment_inscriptionsenderadresse_xml" xml:space="preserve">
    <value> Expéditeur de l'email pour inscription</value>
  </data>
  <data name="emailerror_comment_passwordcopyadresse_xml" xml:space="preserve">
    <value>Expéditeur de l'email pour mot de passe oublié</value>
  </data>
  <data name="kiosq_comment_weboperatorid_xml" xml:space="preserve">
    <value>Opérateur utilisé par le KIOSQ</value>
  </data>
  <data name="kiosq_comment_filiereid_xml" xml:space="preserve">
    <value>ID de la filière</value>
  </data>
  <data name="kiosq_comment_tarifidref_xml" xml:space="preserve">
    <value>ID de référence du tarif</value>
  </data>
  <data name="kiosq_comment_cbmodeidid_xml" xml:space="preserve">
    <value>ID du mode de carte bancaire</value>
  </data>
  <data name="kiosq_comment_postid_xml" xml:space="preserve">
    <value>ID du point de vente</value>
  </data>
  <data name="prisepacessurplan_comment_ismultizones_xml" xml:space="preserve">
    <value>Vente Multizone</value>
  </data>
  <data name="createprofilabo_comment_webfiliereid_xml" xml:space="preserve">
    <value>Abonnement - Créer Profil - Filière</value>
  </data>
  <data name="createprofil_comment_mailunicity_xml" xml:space="preserve">
    <value>(-1 =&gt; désactiver) : Email unique ou non, 0 NON, 1 OUI pour client</value>
  </data>
  <data name="createprofil_comment_webfiliereid_xml" xml:space="preserve">
    <value> Vente en ligne - Créer Profil - Filière</value>
  </data>
  <data name="createprofillink_comment_mailunicity_xml " xml:space="preserve">
    <value>Unicité de l'email pour le profil</value>
  </data>
  <data name="emailsupportingdocuments_comment_senderadresse_xml" xml:space="preserve">
    <value> Adresse expéditrice</value>
  </data>
  <data name="emailsupportingdocuments_comment_receiveradresse_xml" xml:space="preserve">
    <value>Adresse destinataire</value>
  </data>
  <data name="emailsupportingdocuments_comment_replyadresse_xml" xml:space="preserve">
    <value>Adresse pour réponse</value>
  </data>
  <data name="emailsupportingdocuments_comment_copyadresse_xml" xml:space="preserve">
    <value> Adresse de copie</value>
  </data>
  <data name="emailsupportingdocuments_comment_smtpclientip_xml" xml:space="preserve">
    <value>IP du serveur SMTP client</value>
  </data>
  <data name="supportingdocuments_comment_sizeattachments_xml" xml:space="preserve">
    <value>Poids maximum accepté pour l’upload</value>
  </data>
  <data name="edcampaign_comment_presta_xml" xml:space="preserve">
    <value>Liste des prestataires emailing</value>
  </data>
  <data name="edcampaign_comment_eccmkey_xml" xml:space="preserve">
    <value>Clé ECCM pour l’intégration</value>
  </data>
  <data name="specifique_comment_generernumerofacture_xml" xml:space="preserve">
    <value>Génération automatique des numéros de facture</value>
  </data>
  <data name="specifique_comment_flagdesplacesdanspaiement_xml" xml:space="preserve">
    <value>Indicateur pour gérer les places lors du paiement</value>
  </data>
  <data name="specifique_comment_seanceidnotset_xml" xml:space="preserve">
    <value>ID de séance non défini</value>
  </data>
  <data name="acomptes_comment_modespaiement_xml" xml:space="preserve">
    <value>ID Mode de paiement utilisé pour l’acompte</value>
  </data>
  <data name="abov2_comment_offreidhorsabo_xml" xml:space="preserve">
    <value>Id de l’offre en hors Abo accessible sur la plateforme Abo</value>
  </data>
  <data name="paypalconnect_comment_username_xml" xml:space="preserve">
    <value> Nom d'utilisateur pour PayPal Connect</value>
  </data>
  <data name="paypalconnect_comment_password_xml" xml:space="preserve">
    <value>Mot de passe pour PayPal Connect</value>
  </data>
  <data name="facebook_comment_applicationsecret_xml" xml:space="preserve">
    <value>Secret de l’application Facebook</value>
  </data>
  <data name="facebookdev_comment_applicationsecret_xml" xml:space="preserve">
    <value>Secret de l’application Facebook en dev</value>
  </data>
  <data name="services_comment_opinion_order_xml" xml:space="preserve">
    <value>Affichage de l’opinion order sur le site de paiement(1 pour activer 0 pour désactiver)</value>
  </data>
  <data name="coupefile_comment_futures_events_sessions_duration_xml" xml:space="preserve">
    <value>Durée des sessions pour les événements futurs</value>
  </data>
  <data name="coupefile_comment_message_commentaire_xml" xml:space="preserve">
    <value> Message de commentaire</value>
  </data>
  <data name="boutique_comment_delaicachehome_xml" xml:space="preserve">
    <value>Temps du cache pour la boutique home</value>
  </data>
  <data name="boutique_comment_delaicachefamille_xml" xml:space="preserve">
    <value>Temps du cache pour la page famille</value>
  </data>
  <data name="boutique_comment_delaicachesousfamille_xml " xml:space="preserve">
    <value> Temps du cache pour la page sous famille</value>
  </data>
  <data name="boutique_comment_delaicachedetail_xml" xml:space="preserve">
    <value>Temps du cache pour la page détail</value>
  </data>
  <data name="boutique_comment_delaicachemenu_xml" xml:space="preserve">
    <value>Temps du cache pour le menu</value>
  </data>
  <data name="revendeur_comment_ticketacapi_url_xml" xml:space="preserve">
    <value>Url pour interface temps réel avec revendeur Ticketac</value>
  </data>
  <data name="revendeur_comment_ticketacapi_user_xml" xml:space="preserve">
    <value>idUser pour interface temps réel avec revendeur Ticketac</value>
  </data>
  <data name="revendeur_comment_ticketacapi_passw_xml" xml:space="preserve">
    <value> Mot de passe pour interface temps réel avec revendeur Ticketac</value>
  </data>
  <data name="formule_comment_listeformuleid_xml" xml:space="preserve">
    <value>ID de la liste de formules disponibles</value>
  </data>
  <data name="cbmodeidxfois_comment_idxfois_xml" xml:space="preserve">
    <value>Index de paiement en plusieurs fois</value>
  </data>
  <data name="aexpta_comment_url_xml" xml:space="preserve">
    <value>URL pour la plateforme de paiement Aexpta</value>
  </data>
  <data name="aexpta_comment_hmac_xml" xml:space="preserve">
    <value>Clé HMAC pour les transactions sécurisées</value>
  </data>
  <data name="aexpta_comment_blowfish_xml" xml:space="preserve">
    <value> Clé Blowfish pour le chiffrement</value>
  </data>
  <data name="aexpta_comment_merchantid_xml" xml:space="preserve">
    <value> Identifiant du marchand</value>
  </data>
  <data name="reabo_comment_formule_xml" xml:space="preserve">
    <value>Formule de réabonnement</value>
  </data>
  <data name="reabo_comment_dossieretatcheckabo_xml" xml:space="preserve">
    <value> Etat du dossier pour le contrôle d’abonnement</value>
  </data>
  <data name="reabo_comment_updatefansonstructure_xml" xml:space="preserve">
    <value> Mise à jour des fans dans la structure</value>
  </data>
  <data name="reabo_comment_interdireinfocompupdatefans_xml" xml:space="preserve">
    <value> Interdire la mise à jour des informations complémentaires pour les fans</value>
  </data>
  <data name="atos64_comment_paiementenxfois_xml" xml:space="preserve">
    <value>Mode de paiement en plusieurs fois</value>
  </data>
  <data name="indiv_comment_openprofilacheteurpopup_xml" xml:space="preserve">
    <value>Afficher le profil de l’acheteur en popup</value>
  </data>
  <data name="indiv_comment_forcerloginlistemanifs_xml" xml:space="preserve">
    <value>Anmeldung für Veranstaltungsübersicht erzwingen</value>
  </data>
  <data name="indiv_comment_forcerloginpanier_xml" xml:space="preserve">
    <value> Forcer la connexion pour le panier</value>
  </data>
  <data name="emailkiosk_comment_senderadresse_xml" xml:space="preserve">
    <value>Adresse de l’expéditeur</value>
  </data>
  <data name="emailkiosk_comment_replyadresse_xml" xml:space="preserve">
    <value>Adresse pour les réponses</value>
  </data>
  <data name="emailkiosk_comment_copyadresse_xml" xml:space="preserve">
    <value>Adresse pour les copies</value>
  </data>
  <data name="emailkiosk_comment_trakingreceiver_xml" xml:space="preserve">
    <value>Adresse pour le suivi</value>
  </data>
  <data name="emailkiosk_comment_overwriteuseradresse_xml" xml:space="preserve">
    <value>Adresse pour écraser l’utilisateur</value>
  </data>
  <data name="emailinscription_comment_senderadresse_xml" xml:space="preserve">
    <value>Expéditeur de l’email d'inscription</value>
  </data>
  <data name="emailinscription_comment_replyadresse_xml" xml:space="preserve">
    <value>Adresse pour les réponses</value>
  </data>
  <data name="emailinscription_comment_copyadresse_xml" xml:space="preserve">
    <value>Adresse pour les copies</value>
  </data>
  <data name="emailseuilmini_comment_sendadresse_xml" xml:space="preserve">
    <value> Expéditeur de l’email</value>
  </data>
  <data name="emailseuilmini_comment_senderadresse_xml" xml:space="preserve">
    <value>Expéditeur de l’email</value>
  </data>
  <data name="emailseuilmini_comment_replyadresse_xml" xml:space="preserve">
    <value>Adresse pour les réponses</value>
  </data>
  <data name="emailseuilmini_comment_copyadresse_xml" xml:space="preserve">
    <value>Adresse pour les copies</value>
  </data>
  <data name="revendeur_comment_duration_xml" xml:space="preserve">
    <value>Durée des manifestations en minutes (configurable requête) - coupe file revendeur</value>
  </data>
  <data name="revendeur_comment_message_commentaire_xml" xml:space="preserve">
    <value>Message pour téléchargement depuis Coupe file</value>
  </data>
  <data name="smsefidem_comment_appid_xml" xml:space="preserve">
    <value> Identifiant de l'application</value>
  </data>
  <data name="smsefidem_comment_login_xml" xml:space="preserve">
    <value>Identifiant de connexion</value>
  </data>
  <data name="smsefidem_comment_passw_xml" xml:space="preserve">
    <value> Mot de passe</value>
  </data>
  <data name="crmexterne_comment_type_xml" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="crmexterne_comment_salt_xml" xml:space="preserve">
    <value>Salt</value>
  </data>
  <data name="crmexterne_comment_url_xml" xml:space="preserve">
    <value> URL</value>
  </data>
  <data name="unidy_comment_secret_xml" xml:space="preserve">
    <value> Secret</value>
  </data>
  <data name="unidy_comment_client_xml" xml:space="preserve">
    <value> Client</value>
  </data>
  <data name="unidy_comment_redirecturl_xml" xml:space="preserve">
    <value>URL de redirection</value>
  </data>
  <data name="unidy_comment_urltokenservice_xml" xml:space="preserve">
    <value>URL du service de tokens</value>
  </data>
  <data name="unidy_comment_urluinfo_xml" xml:space="preserve">
    <value>URL pour obtenir des informations utilisateur</value>
  </data>
  <data name="virement_comment_hourslimitation_xml" xml:space="preserve">
    <value> Limitation des heure</value>
  </data>
  <data name="assurancearteo_comment_senderadresse_xml" xml:space="preserve">
    <value>Expéditeur de l'email</value>
  </data>
  <data name="assurancearteo_comment_replyadresse_xml" xml:space="preserve">
    <value>Adresse pour les réponses</value>
  </data>
  <data name="assurancearteo_comment_copyadresse_xml" xml:space="preserve">
    <value>Adresse pour les copies</value>
  </data>
  <data name="assurancearteo_comment_smtpclientip_xml" xml:space="preserve">
    <value>Adresse IP du client SMTP</value>
  </data>
  <data name="pelecard_comment_currency_xml" xml:space="preserve">
    <value>Devise</value>
  </data>
  <data name="pelecard_comment_login_xml" xml:space="preserve">
    <value> Identifiant de connexion</value>
  </data>
  <data name="pelecard_comment_terminalid_xml" xml:space="preserve">
    <value> Identifiant du terminal</value>
  </data>
  <data name="pelecard_comment_password_xml" xml:space="preserve">
    <value> Passwort</value>
  </data>
  <data name="pelecard_comment_actiontype_xml" xml:space="preserve">
    <value> Type d'action</value>
  </data>
  <data name="pelecard_comment_url_xml" xml:space="preserve">
    <value>URL</value>
  </data>
  <data name="pelecard_comment_acceptedlanguage_xml " xml:space="preserve">
    <value>Langue acceptée</value>
  </data>
  <data name="paypalapi_comment_currency_xml" xml:space="preserve">
    <value> Währung</value>
  </data>
  <data name="paypalapi_comment_accesstoken_xml" xml:space="preserve">
    <value>Jeton d'accès</value>
  </data>
  <data name="paypalapi_comment_mode_xml" xml:space="preserve">
    <value>Mode</value>
  </data>
  <data name="paramprintathome_comment_ticketeos_urlkombiticket_xml" xml:space="preserve">
    <value>URL pour le ticket KombiTicket</value>
  </data>
  <data name="paramprintathome_comment_ticketeos_creduser_xml" xml:space="preserve">
    <value>Identifiant utilisateur</value>
  </data>
  <data name="paramprintathome_comment_ticketeos_credpassw_xml" xml:space="preserve">
    <value> Mot de passe utilisateur</value>
  </data>
  <data name="paramprintathome_comment_ticketeos_token_xml" xml:space="preserve">
    <value>Jeton</value>
  </data>
  <data name="paramprintathome_comment_ticketeos_systemid_xml" xml:space="preserve">
    <value>ID du système</value>
  </data>
  <data name="paramprintathome_comment_ticketeos_organizerid_xml" xml:space="preserve">
    <value>ID de l'organisateur Organizer</value>
  </data>
  <data name="emailpassword_comment_senderadresse_xml" xml:space="preserve">
    <value> Expéditeur de l'email</value>
  </data>
  <data name="emailpassword_comment_replyadresse_xml" xml:space="preserve">
    <value>Adresse pour les réponses</value>
  </data>
  <data name="emailpassword_comment_copyadresse_xml" xml:space="preserve">
    <value> Adresse pour les copies</value>
  </data>
  <data name="contraintesventes_comment_restrictiontarifparfichier_xml" xml:space="preserve">
    <value>Restriction tarifaire par fichier</value>
  </data>
  <data name="customerarea_comment_version_xml" xml:space="preserve">
    <value>Version de l'espace client</value>
  </data>
  <data name="customerarea_comment_checkinfocomplogin_xml" xml:space="preserve">
    <value> Connexion InfoComp</value>
  </data>
  <data name="customerarea_comment_listinfocompdisponible_xml" xml:space="preserve">
    <value>Liste des infocomp disponibles</value>
  </data>
  <data name="createdossierproduits_comment_filiereid_xml" xml:space="preserve">
    <value>Abonnement - Create Profil - Filière</value>
  </data>
  <data name="cbmodeid_comment_xfois_xml" xml:space="preserve">
    <value>Id mode de paiement pour paiement en plusieurs fois</value>
  </data>
  <data name="cbmodeid_comment_sofortberweisungde_xml" xml:space="preserve">
    <value>Id mode de paiement SOFORT</value>
  </data>
  <data name="cbmodeid_comment_id_xml" xml:space="preserve">
    <value> Id mode de paiement CB</value>
  </data>
  <data name="cbmodeid_comment_mastercard_xml" xml:space="preserve">
    <value>Id mode de paiement MasterCard</value>
  </data>
  <data name="cbmodeid_comment_visa_xml" xml:space="preserve">
    <value>9930002</value>
  </data>
  <data name="cbmodeid_comment_ecard_xml" xml:space="preserve">
    <value> Id mode de paiement E_CARD</value>
  </data>
  <data name="cbmodeid_comment_paypal_xml" xml:space="preserve">
    <value> Id mode de paiement PAYPAL</value>
  </data>
  <data name="cbmodeid_comment_cvco_xml" xml:space="preserve">
    <value>Id mode de paiement CVCO</value>
  </data>
  <data name="cbmodeid_comment_multi_xml" xml:space="preserve">
    <value> Id mode de paiement MULTI</value>
  </data>
  <data name="cbmodeid_comment_sofort_xml" xml:space="preserve">
    <value>Id mode de paiement SOFORT</value>
  </data>
  <data name="cbmodeid_comment_pa15_sofortberweisungde_xml" xml:space="preserve">
    <value>Id mode de paiement SOFORT</value>
  </data>
  <data name="cbmodeid_comment_na_xml" xml:space="preserve">
    <value>Id mode de paiement Facture ou Gratuit</value>
  </data>
  <data name="cbmodeid_comment_pa_cb_xml" xml:space="preserve">
    <value> Id mode de paiement CB</value>
  </data>
  <data name="cbmodeid_comment_pa_paypal_xml" xml:space="preserve">
    <value>Id mode de paiement PAYPAL</value>
  </data>
  <data name="cbmodeid_comment_pa_sofortberweisungde_xml" xml:space="preserve">
    <value>Id mode de paiement SOFORT</value>
  </data>
  <data name="cbmodeid_comment_pa_visa_xml" xml:space="preserve">
    <value>Id mode de paiement VISA</value>
  </data>
  <data name="toast_message_connection_error_title" xml:space="preserve">
    <value>Erreur de connexion </value>
  </data>
  <data name="toast_message_connection_error_body" xml:space="preserve">
    <value>Échec de la connexion. Veuillez choisir une autre structure.</value>
  </data>
  <data name="button_translations_terms" xml:space="preserve">
    <value>Traduction des widgets</value>
  </data>
  <data name="button_sections" xml:space="preserve">
    <value>Traduction des sections</value>
  </data>
  <data name="button_variables" xml:space="preserve">
    <value>Traduction des variables</value>
  </data>
  <data name="modal_title_edit_section" xml:space="preserve">
    <value>Modifier la section</value>
  </data>
  <data name="confirm_delete" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir supprimer la variable '{0}'?</value>
  </data>
  <data name="delete_success" xml:space="preserve">
    <value>La variable &lt;strong&gt;'{0}'&lt;/strong&gt; a été supprimée avec succès.</value>
  </data>
  <data name="delete_error" xml:space="preserve">
    <value>Erreur lors de la suppression de la variable &lt;strong&gt;'{0}' : {1}&lt;/strong&gt;</value>
  </data>
  <data name="confirm_delete_translation_area" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir supprimer la variable '{0}' ?</value>
  </data>
  <data name="edit_partenaire" xml:space="preserve">
    <value>Éditer un partenaire</value>
  </data>
  <data name="added_sections" xml:space="preserve">
    <value>Ajouter des sections</value>
  </data>
  <data name="mandatory_sections" xml:space="preserve">
    <value>Configuration obligatoire</value>
  </data>
  <data name="advanced_configuration" xml:space="preserve">
    <value>Sections complémentaires</value>
  </data>
  <data name="confirm_dialog_yes_button" xml:space="preserve">
    <value>Oui</value>
  </data>
  <data name="confirm_dialog_no_button" xml:space="preserve">
    <value>Non</value>
  </data>
  <data name="time_warning_message" xml:space="preserve">
    <value>Le fichier sera supprimé dans : {0} minute(s) et {1} seconde(s). Voulez-vous continuer ?</value>
  </data>
  <data name="time_warning_title" xml:space="preserve">
    <value>Avertissement de temps</value>
  </data>
  <data name="toast_message_title_file_deletion" xml:space="preserve">
    <value>Suppression de fichier</value>
  </data>
  <data name="toast_message_file_deletion" xml:space="preserve">
    <value>Le fichier temporaire a été supprimé car le temps est écoulé.</value>
  </data>
  <data name="selected_variables" xml:space="preserve">
    <value>Variables sélectionnées</value>
  </data>
  <data name="translation_choose_variables" xml:space="preserve">
    <value>Choisir des variables</value>
  </data>
  <data name="translation_variable_fully_selected" xml:space="preserve">
    <value>Entièrement sélectionné</value>
  </data>
  <data name="translation_variable_partially_selected" xml:space="preserve">
    <value>Partiellement sélectionné</value>
  </data>
  <data name="translation_variable_not_selected" xml:space="preserve">
    <value>Non sélectionné</value>
  </data>
  <data name="Field_Validation_SingleEmail" xml:space="preserve">
    <value>Le champ ne peut contenir qu'une seule adresse e-mail</value>
  </data>
  <data name="Field_Validation_ReplyAddress_SingleEmail" xml:space="preserve">
    <value>Le champ REPLYADRESSE ne peut contenir qu'une seule adresse e-mail</value>
  </data>
  <data name="Field_Validation_InvalidEmail" xml:space="preserve">
    <value>Adresse e-mail invalide</value>
  </data>
  <data name="Field_Validation_InvalidEmail_WithDetails" xml:space="preserve">
    <value>Adresse e-mail invalide : {0}</value>
  </data>
  <data name="Field_Validation_DuplicateEmail" xml:space="preserve">
    <value>Adresse e-mail en double détectée.</value>
  </data>
  <data name="Field_Validation_DuplicateEmail_WithDetails" xml:space="preserve">
    <value>Adresses e-mail en double détectées : {0}</value>
  </data>
  <data name="close_modal" xml:space="preserve">
    <value>Fermer</value>
  </data>
  <data name="toast_message_title_success" xml:space="preserve">
    <value>Succès</value>
  </data>
  <data name="toast_message_title_error" xml:space="preserve">
    <value>erreur </value>
  </data>
  <data name="toast_message_form_submission_failed" xml:space="preserve">
    <value> Échec de la soumission du formulaire : {0}</value>
  </data>
  <data name="toast_message_form_validation_failed" xml:space="preserve">
    <value> Le formulaire contient des erreurs de validation.</value>
  </data>
  <data name="gestion-webtracing" xml:space="preserve">
    <value>Gestion des webtracings</value>
  </data>
  <data name="username_nom_specifie_webtracing" xml:space="preserve">
    <value>Non spécifié</value>
  </data>
  <data name="toast_message_structure_failed" xml:space="preserve">
    <value>Échec du chargement des données de la structure</value>
  </data>
  <data name="toast_message_title_delete_structure_success" xml:space="preserve">
    <value>Suppression de la structure réussie</value>
  </data>
  <data name="toast_message_delete_success" xml:space="preserve">
    <value>La structure a été supprimée avec succès.</value>
  </data>
  <data name="toast_message_title_delete_structure_error" xml:space="preserve">
    <value>Erreur de suppression de la structure</value>
  </data>
  <data name="toast_message_delete_structure_failed" xml:space="preserve">
    <value>Échec de la suppression de la structure</value>
  </data>
  <data name="webtracing_list_title" xml:space="preserve">
    <value>Liste des Structures avec Connexions WebTracing</value>
  </data>
  <data name="webtracing_no_structure_found" xml:space="preserve">
    <value>Aucune structure ou connexion trouvée.</value>
  </data>
  <data name="webtracing_deleted" xml:space="preserve">
    <value>Supprimée</value>
  </data>
  <data name="webtracing_not_deleted" xml:space="preserve">
    <value>Non</value>
  </data>
  <data name="webtracing_unspecified" xml:space="preserve">
    <value>Non spécifié</value>
  </data>
  <data name="webtracing_column_name" xml:space="preserve">
    <value>Nom</value>
  </data>
  <data name="webtracing_column_deleted" xml:space="preserve">
    <value>Supprimée</value>
  </data>
  <data name="webtracing_column_deletion_date" xml:space="preserve">
    <value>Date de suppression</value>
  </data>
  <data name="webtracing_column_deleted_by" xml:space="preserve">
    <value>Supprimée par</value>
  </data>
  <data name="webtracing_column_database" xml:space="preserve">
    <value>Base de données</value>
  </data>
  <data name="webtracing_column_start_date" xml:space="preserve">
    <value>Date de début </value>
  </data>
  <data name="webtracing_column_stop_date" xml:space="preserve">
    <value>Date de fin</value>
  </data>
  <data name="webtracing_column_actions" xml:space="preserve">
    <value>Actions</value>
  </data>
  <data name="webtracing_button_deleted" xml:space="preserve">
    <value>Supprimée</value>
  </data>
  <data name="webtracing_button_delete" xml:space="preserve">
    <value>À supprimer</value>
  </data>
  <data name="role-management" xml:space="preserve">
    <value>Gestion des rôles</value>
  </data>
  <data name="modal_success_message" xml:space="preserve">
    <value>coupons ont été générés avec succès et enregistrés dans la base de données.</value>
  </data>
  <data name="rolemanagement_create_role" xml:space="preserve">
    <value>Créer un Rôle</value>
  </data>
  <data name="new-module" xml:space="preserve">
    <value>Préfixe</value>
  </data>
  <data name="rolemanagement_create_group" xml:space="preserve">
    <value>Créer un groupe</value>
  </data>
  <data name="role_create_new" xml:space="preserve">
    <value>Créer un nouveau rôle</value>
  </data>
  <data name="rolemanagement_create_module" xml:space="preserve">
    <value>Créer un  Module</value>
  </data>
  <data name="rolemanagement_groupname" xml:space="preserve">
    <value>Nom du Groupe</value>
  </data>
  <data name="rolemanagement_groupid" xml:space="preserve">
    <value>ID du Groupe</value>
  </data>
  <data name="rolemanagement_modulename" xml:space="preserve">
    <value>Nom du module </value>
  </data>
  <data name="rolemanagement_modulecomment" xml:space="preserve">
    <value>Commentaire</value>
  </data>
  <data name="rolemanagement_rolename" xml:space="preserve">
    <value>Nom du Rôle</value>
  </data>
  <data name="rolemanagement_canread" xml:space="preserve">
    <value>Peut Lire</value>
  </data>
  <data name="rolemanagement_cancreate" xml:space="preserve">
    <value>Peut Créer</value>
  </data>
  <data name="rolemanagement_canupdate" xml:space="preserve">
    <value>Peut Mettre à Jour</value>
  </data>
  <data name="rolemanagement_candelete" xml:space="preserve">
    <value>Peut Supprimer</value>
  </data>
  <data name="rolemanagement_level" xml:space="preserve">
    <value>Niveau</value>
  </data>
  <data name="rolemanagement_title" xml:space="preserve">
    <value>Gestion des Rôles et Modules</value>
  </data>
  <data name="rolemanagement_loading" xml:space="preserve">
    <value>Veuillez patienter pendant le chargement des données...</value>
  </data>
  <data name="rolemanagement_module" xml:space="preserve">
    <value>Module </value>
  </data>
  <data name="rolemanagement_role" xml:space="preserve">
    <value>Rôle</value>
  </data>
  <data name="rolemanagement_no_module" xml:space="preserve">
    <value>Aucun Rôte</value>
  </data>
  <data name="rolemanagement_no_module_or_role" xml:space="preserve">
    <value>Aucun Module ou Rôle pour {0}</value>
  </data>
  <data name="rolemanagement_assign_roles" xml:space="preserve">
    <value>Attribution de Rôles</value>
  </data>
  <data name="rolemanagement_modules" xml:space="preserve">
    <value>Modules</value>
  </data>
  <data name="rolemanagement_ad_groups" xml:space="preserve">
    <value>Groupes AD</value>
  </data>
  <data name="rolemanagement_roles" xml:space="preserve">
    <value>Rôles</value>
  </data>
  <data name="rolemanagement_choose_option" xml:space="preserve">
    <value>Choisir une option</value>
  </data>
  <data name="rolemanagement_assign" xml:space="preserve">
    <value>Attribuer</value>
  </data>
  <data name="error_create_group_title" xml:space="preserve">
    <value>Erreur lors de la création du groupe</value>
  </data>
  <data name="error_create_group_message" xml:space="preserve">
    <value>Impossible de créer le groupe {0}. Veuillez réessayer.</value>
  </data>
  <data name="Serror_create_module_title" xml:space="preserve">
    <value>Erreur lors de la création du module</value>
  </data>
  <data name="error_create_module_message" xml:space="preserve">
    <value>Impossible de créer le module {0}. Veuillez réessayer.</value>
  </data>
  <data name="error_create_role_title" xml:space="preserve">
    <value>Erreur lors de la création du rôle</value>
  </data>
  <data name="error_create_role_message" xml:space="preserve">
    <value>Impossible de créer le rôle {0}. Veuillez réessayer.</value>
  </data>
  <data name="error_loading_data_title" xml:space="preserve">
    <value>Erreur de chargement des données</value>
  </data>
  <data name="error_loading_data_message" xml:space="preserve">
    <value>Une erreur est survenue lors du chargement des données : {0}</value>
  </data>
  <data name="success_roles_assigned_title" xml:space="preserve">
    <value>Attribution des rôles réussie</value>
  </data>
  <data name="success_roles_assigned_message" xml:space="preserve">
    <value>Les rôles ont été attribués avec succès.</value>
  </data>
  <data name="error_assign_roles_title" xml:space="preserve">
    <value>Erreur lors de l'attribution des rôles</value>
  </data>
  <data name="error_assign_roles_message" xml:space="preserve">
    <value>Impossible d'attribuer les rôles : {0}</value>
  </data>
  <data name="error_group_exists" xml:space="preserve">
    <value>Le groupe {0} existe déjà.</value>
  </data>
  <data name="error_module_exists" xml:space="preserve">
    <value>Le module {0} existe déjà.</value>
  </data>
  <data name="error_role_exists" xml:space="preserve">
    <value>Le rôle {0} existe déjà.</value>
  </data>
  <data name="choisi_structure" xml:space="preserve">
    <value>Choisir</value>
  </data>
  <data name="error_search_partners_title" xml:space="preserve">
    <value>Erreur lors de la recherche des partenaires</value>
  </data>
  <data name="no_roles" xml:space="preserve">
    <value>Aucun rôle</value>
  </data>
  <data name="label_roles" xml:space="preserve">
    <value>Rôles</value>
  </data>
  <data name="label_structures" xml:space="preserve">
    <value>Structures</value>
  </data>
  <data name=" no_structures" xml:space="preserve">
    <value>Aucune structures</value>
  </data>
  <data name="no_partner_structures" xml:space="preserve">
    <value>Aucune  structures</value>
  </data>
  <data name="Stradd_missing_keys" xml:space="preserve">
    <value>Ajouter les clés manquantes</value>
  </data>
  <data name="toast_add_missing_keys_success" xml:space="preserve">
    <value>Les clés manquantes ont été ajoutées avec succès</value>
  </data>
  <data name="add_missing_keys_title" xml:space="preserve">
    <value>Ajouter les clés manquantes</value>
  </data>
  <data name="add_missing_keys_description" xml:space="preserve">
    <value>Certaines clés sont manquantes. Voulez-vous les ajouter ?</value>
  </data>
  <data name="for_all_languages" xml:space="preserve">
    <value>Pour toutes les langues</value>
  </data>
  <data name="for_this_language" xml:space="preserve">
    <value>Pour cette langue</value>
  </data>
  <data name="add_missing_keys" xml:space="preserve">
    <value>Ajouter les clés manquantes</value>
  </data>
  <data name="payment_platform_translation" xml:space="preserve">
    <value>Traduction de la plateforme de paiement</value>
  </data>
  <data name="copy_to_test" xml:space="preserve">
    <value>Copier vers TEST</value>
  </data>
  <data name="copy_to_prod" xml:space="preserve">
    <value>Copier vers PROD</value>
  </data>
  <data name="copy_to_prod_confirmation" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir copier les données vers PROD ?</value>
  </data>
  <data name="copy_to_test_confirmation" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir copier les données vers TEST ?</value>
  </data>
  <data name="copy_to_prod_title" xml:space="preserve">
    <value>Copier vers PROD</value>
  </data>
  <data name="copy_to_test_title" xml:space="preserve">
    <value>Copier vers TEST</value>
  </data>
  <data name="toast_copy_to_test_success" xml:space="preserve">
    <value>Toast de copie vers test réussie</value>
  </data>
  <data name="toast_copy_to_prod_success" xml:space="preserve">
    <value>Copier DEV vers PROD réussi</value>
  </data>
  <data name="admin_platform_translation" xml:space="preserve">
    <value>Traduction de la plateforme Admin</value>
  </data>
  <data name="cbmodeid_comment_pa_paybox_xml" xml:space="preserve">
    <value>paiement, deux comptes pour un prestataire</value>
  </data>
  <data name="cbmodeid_comment_pa_paybox_url_redirection_xml" xml:space="preserve">
    <value>Url de redirection vers le prestataire de paiement</value>
  </data>
  <data name="cbmodeid_comment_pa_paybox_code_devise_xml" xml:space="preserve">
    <value>Code devise du site qui sera envoyé au prestataire</value>
  </data>
  <data name="cbmodeid_comment_pa_paybox_identifiant_structure_xml" xml:space="preserve">
    <value>Identifiant de la structure</value>
  </data>
  <data name="cbmodeid_comment_pa_paybox_code_site_xml" xml:space="preserve">
    <value>Code site (fourni par PAYBOX</value>
  </data>
  <data name="cbmodeid_comment_pa_paybox_rang_site_xml" xml:space="preserve">
    <value>Rang du site (fourni par PAYBOX)</value>
  </data>
  <data name="cbmodeid_comment_pa_paybox2_xml" xml:space="preserve">
    <value>paiement, deux comptes pour un prestataire</value>
  </data>
  <data name="cbmodeid_comment_pa_paybox2_url_redirection_xml" xml:space="preserve">
    <value>Url de redirection vers le prestataire de paiemen</value>
  </data>
  <data name="cbmodeid_comment_pa_paybox2_code_devise_xml" xml:space="preserve">
    <value>Code devise du site qui sera envoyé au prestataire</value>
  </data>
  <data name="cbmodeid_comment_pa_paybox2_code_site_xml" xml:space="preserve">
    <value>Code site (fourni par PAYBOX)</value>
  </data>
  <data name="cbmodeid_comment_pa_paybox2_rang_site_xml" xml:space="preserve">
    <value>Rang du site (fourni par PAYBOX)</value>
  </data>
  <data name="cbmodeid_comment_pa_paybox2_identifiant_structure_xml" xml:space="preserve">
    <value> Identifiant de la structure</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac1_xml" xml:space="preserve">
    <value>paiement avec HMAC, deux comptes pour un prestataire</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac1_url_redirection_xml" xml:space="preserve">
    <value> Url de redirection vers le prestataire de paiement</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac1_code_devise_xml" xml:space="preserve">
    <value>Code devise du site qui sera envoyé au prestataire</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac1_code_site_xml" xml:space="preserve">
    <value>Code site (fourni par PAYBOXHMAC</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac1_rang_site_xml" xml:space="preserve">
    <value>Rang du site (fourni par PAYBOXHMAC</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac1_identifiant_structure_xml" xml:space="preserve">
    <value>Identifiant de la structure</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac1_hmac_xml" xml:space="preserve">
    <value>HMAC key used to secure the transaction</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac2_xml" xml:space="preserve">
    <value> paiement avec HMAC, deux comptes pour un prestataire</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac2_url_redirection_xml" xml:space="preserve">
    <value>Url de redirection vers le prestataire de paiement</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac2_url_redirection_old_xml" xml:space="preserve">
    <value>Ancienne URL de redirection vers le prestataire de paiement</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac2_code_devise_xml" xml:space="preserve">
    <value> Code devise du site qui sera envoyé au prestataire</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac2_code_site_xml" xml:space="preserve">
    <value>Code site (fourni par PAYBOXHMAC)</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac2_rang_site_xml" xml:space="preserve">
    <value>Rang du site (fourni par PAYBOXHMAC)</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac2_identifiant_structure_xml" xml:space="preserve">
    <value>Identifiant de la structure</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac2_url_acquit_xml" xml:space="preserve">
    <value>URL d’acquittement de la transaction</value>
  </data>
  <data name="cbmodeid_comment_pa_payboxhmac2_hmac_xml" xml:space="preserve">
    <value>Clé HMAC utilisée pour sécuriser la transaction</value>
  </data>
  <data name="cbmodeid_comment_pa_cybermuth1_xml" xml:space="preserve">
    <value> Paiement via CYBERMUTH, intégration spécifique</value>
  </data>
  <data name="cbmodeid_comment_pa_cybermuth1_url_redirection_xml" xml:space="preserve">
    <value>Url de redirection vers le prestataire de paiement</value>
  </data>
  <data name="cbmodeid_comment_pa_cybermuth1_code_devise_xml" xml:space="preserve">
    <value>Code devise du site qui sera envoyé au prestataire</value>
  </data>
  <data name="cbmodeid_comment_pa_cybermuth1_identifiant_structure_xml" xml:space="preserve">
    <value>Identifiant de la structure</value>
  </data>
  <data name="cbmodeid_comment_pa_cybermuth1_code_site_xml" xml:space="preserve">
    <value>Code site (fourni par CYBERMUTH)</value>
  </data>
  <data name="cbmodeid_comment_pa_cybermuth1_version_xml" xml:space="preserve">
    <value>Version du protocole utilisé</value>
  </data>
  <data name="cbmodeid_comment_pa_cybermuth1_key_xml" xml:space="preserve">
    <value>Clé de sécurité pour les transactions</value>
  </data>
  <data name="cbmodeid_comment_pa_cybermuth1_langage_xml" xml:space="preserve">
    <value> Langage utilisé pour la transaction</value>
  </data>
  <data name="cbmodeid_comment_pa_cybermuth1_hashalgo_xml" xml:space="preserve">
    <value>Algorithme de hachage utilisé pour la sécurité</value>
  </data>
  <data name="cbmodeid_comment_pa_linkapiaccuse_xml" xml:space="preserve">
    <value>Lien utilisé pour demander la capture de la carte après bonne réception</value>
  </data>
  <data name="cbmodeid_comment_pa_apipassword_xml" xml:space="preserve">
    <value>Mot de passe utilisé avec LINKAPIACCUSE</value>
  </data>
  <data name="cbmodeid_comment_pa_apilogin_xml" xml:space="preserve">
    <value> Utilisateur utilisé avec LINKAPIACCUSE</value>
  </data>
  <data name="cbmodeid_comment_pa_s10reference_xml" xml:space="preserve">
    <value>Renseigner cette clé si le client fait l'objet d'une migration simplifiée chez Atos : ne mettre cette clé que si le contrat de client ne commence pas par 0210</value>
  </data>
  <data name="cbmodeid_comment_pa_versionkey_xml" xml:space="preserve">
    <value>Numéro de version de la clé utilisée (en général = 1, voir backoffice presta client du client)</value>
  </data>
  <data name="cbmodeid_comment_pa_key_xml" xml:space="preserve">
    <value> Clé d'accès (backoffice presta client du client)</value>
  </data>
  <data name="cbmodeid_comment_pa_interfaceversion_xml" xml:space="preserve">
    <value>Version du prestataire de paiement</value>
  </data>
  <data name="cbmodeid_comment_pa_autoripuiscapture_xml" xml:space="preserve">
    <value>Si O, on procède à une autorisation PUIS la capture après validation. Dans ce cas, les 2 clés suivantes doivent être présentes</value>
  </data>
  <data name="cbmodeid_comment_pa_password_xml" xml:space="preserve">
    <value>Secret Key Live ou TEST à trouver dans le backoffice</value>
  </data>
  <data name="cbmodeid_comment_pa_autoripuiscapture_true_false_xml" xml:space="preserve">
    <value>Si 1, on autorise puis capture après validation, sinon, aucune autorisation</value>
  </data>
  <data name="cbmodeid_comment_pa_smsdefaultregion_xml" xml:space="preserve">
    <value>La clé SMSDEFAULTREGION correspond au code pays par défaut pour les campagnes SMS</value>
  </data>
  <data name="cbmodeid_comment_pa_webhooksalt_xml" xml:space="preserve">
    <value> Clé fournie par Sharegroop pour générer la signature lors de la notification</value>
  </data>
  <data name="cbmodeid_comment_pa_rang_xml" xml:space="preserve">
    <value>Rang du site</value>
  </data>
  <data name="modifier-temps-panier" xml:space="preserve">
    <value>Validité panier</value>
  </data>
  <data name="Modifier_Temps_Panier" xml:space="preserve">
    <value>Modifier le temps du panier</value>
  </data>
  <data name="Temps_Expiration_Panier" xml:space="preserve">
    <value>Temps d'expiration du panier(visible par l'utilisateur dans l'Indiv) </value>
  </data>
  <data name="Saisir_Temps_Expiration" xml:space="preserve">
    <value>Saisissez le temps avant expiration du panier (1-1430 minutes).</value>
  </data>
  <data name="Delai_Deflag" xml:space="preserve">
    <value>Délai de deflag (automatique)</value>
  </data>
  <data name="Delai_Deflag_Description" xml:space="preserve">
    <value>Délai de deflag = Temps d'expiration + 10 minutes (calculé automatiquement).</value>
  </data>
  <data name="retour_liste_struture" xml:space="preserve">
    <value>Retour</value>
  </data>
  <data name="config_value_exceedsDB_error" xml:space="preserve">
    <value>La valeur(ConfigIni) ne doit pas être supérieure à la valeur de la BDD.</value>
  </data>
  <data name="common_error" xml:space="preserve">
    <value>Erreur</value>
  </data>
  <data name="temps_panier_saved_successfully" xml:space="preserve">
    <value>Le temps du panier a été enregistré avec succès</value>
  </data>
  <data name="temps_panier_save_error" xml:space="preserve">
    <value>Erreur lors de l'enregistrement du temps du panier : {0}</value>
  </data>
  <data name="delai_deflag_null_error" xml:space="preserve">
    <value>Le délai de déflagration est nul</value>
  </data>
  <data name="panier_expiration_inferieure_deflag" xml:space="preserve">
    <value>Le temps d'expiration ({0}) ne peut pas être inférieur à la valeur de déflagration ({1}).</value>
  </data>
  <data name="gestion-coupons-promo" xml:space="preserve">
    <value>Gestion coupons</value>
  </data>
  <data name="no_profil_message" xml:space="preserve">
    <value>Aucun profil acheteur disponible.</value>
  </data>
  <data name="back_button" xml:space="preserve">
    <value>Retour</value>
  </data>
  <data name="general_title" xml:space="preserve">
    <value>Gestion des coupons promotionnels</value>
  </data>
  <data name="table_header_id" xml:space="preserve">
    <value>ID</value>
  </data>
  <data name="table_header_label" xml:space="preserve">
    <value>Labellé</value>
  </data>
  <data name="table_header_last_name" xml:space="preserve">
    <value>Nom</value>
  </data>
  <data name="table_header_first_name" xml:space="preserve">
    <value>Prénom</value>
  </data>
  <data name="table_header_creation_date" xml:space="preserve">
    <value>Date de Création</value>
  </data>
  <data name="table_header_coupon_status" xml:space="preserve">
    <value>Statut des coupons</value>
  </data>
  <data name="table_header_actions" xml:space="preserve">
    <value>Actions</value>
  </data>
  <data name="coupon_status_active" xml:space="preserve">
    <value>Coupon(s) actif(s)</value>
  </data>
  <data name="coupon_status_expired" xml:space="preserve">
    <value>Statut du coupon expiré.</value>
  </data>
  <data name="coupon_status_none" xml:space="preserve">
    <value>Aucun coupon</value>
  </data>
  <data name="button_add_coupons" xml:space="preserve">
    <value>Ajouter des coupons</value>
  </data>
  <data name="button_view_coupons" xml:space="preserve">
    <value>Voir coupons</value>
  </data>
  <data name="button_export" xml:space="preserve">
    <value>Exporter</value>
  </data>
  <data name="button_generate" xml:space="preserve">
    <value>Generate coupons</value>
  </data>
  <data name="button_close" xml:space="preserve">
    <value>Fermer</value>
  </data>
  <data name="modal_create_title" xml:space="preserve">
    <value>Créer des coupons promo pour</value>
  </data>
  <data name="modal_number_of_coupons" xml:space="preserve">
    <value>Nombre de coupons à générer</value>
  </data>
  <data name="modal_max_coupons_message" xml:space="preserve">
    <value>geVous pouvez générer jusqu'à 20 coupons à la foi</value>
  </data>
  <data name="modal_start_date" xml:space="preserve">
    <value>Date de début</value>
  </data>
  <data name="modal_start_date_error" xml:space="preserve">
    <value>La date de début doit être aujourd'hui ou dans le futur</value>
  </data>
  <data name="modal_end_date" xml:space="preserve">
    <value>Date d'expiration commune</value>
  </data>
  <data name="modal_end_date_error" xml:space="preserve">
    <value>La date d'expiration doit être postérieure à la date de début</value>
  </data>
  <data name="modal_generating" xml:space="preserve">
    <value>Generierung...</value>
  </data>
  <data name="modal_coupons_title" xml:space="preserve">
    <value>Coupons promo pour</value>
  </data>
  <data name="tab_active_coupons" xml:space="preserve">
    <value>Active coupons</value>
  </data>
  <data name="tab_expired_coupons" xml:space="preserve">
    <value>Coupons expirés</value>
  </data>
  <data name="tab_all_coupons" xml:space="preserve">
    <value>Tous les coupons</value>
  </data>
  <data name="tab_no_coupons" xml:space="preserve">
    <value>Aucun coupon trouvé pour ce profil.</value>
  </data>
  <data name="coupon_list_code" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="coupon_list_creation_date" xml:space="preserve">
    <value>Date de création</value>
  </data>
  <data name="coupon_list_start_date" xml:space="preserve">
    <value>Date de début</value>
  </data>
  <data name="coupon_list_expiration_date" xml:space="preserve">
    <value>Date d'expiration</value>
  </data>
  <data name="coupon_list_expires_in" xml:space="preserve">
    <value>Expire dans</value>
  </data>
  <data name="coupon_list_expired_since" xml:space="preserve">
    <value>Expiré depuis</value>
  </data>
  <data name="coupon_list_status" xml:space="preserve">
    <value>status</value>
  </data>
  <data name="coupon_list_active" xml:space="preserve">
    <value>Actif</value>
  </data>
  <data name="coupon_list_expired" xml:space="preserve">
    <value>jour(s)</value>
  </data>
  <data name="coupon_list_days" xml:space="preserve">
    <value>jour(s)</value>
  </data>
  <data name="coupon_list_no_active_coupons" xml:space="preserve">
    <value>Aucun coupon actif</value>
  </data>
  <data name="coupon_list_no_expired_coupons" xml:space="preserve">
    <value>Aucun coupon expiré</value>
  </data>
  <data name="button_export_these_coupons" xml:space="preserve">
    <value>Exporter ces coupons</value>
  </data>
  <data name="modal_success_title" xml:space="preserve">
    <value>Coupons promo générés avec succès pour</value>
  </data>
  <data name="button_exporting" xml:space="preserve">
    <value>Exportation...</value>
  </data>
  <data name="coupon_aucun_oupon_cree" xml:space="preserve">
    <value>Aucun coupon n'a pu être créé. Veuillez réessayer.</value>
  </data>
  <data name="coupon_erreur_creation" xml:space="preserve">
    <value>Erreur lors de la création des coupons : {0}</value>
  </data>
  <data name="coupon_creation_succes" xml:space="preserve">
    <value>{0} coupon(s) créé(s) avec succès pour {1}.</value>
  </data>
  <data name="create_coupon_attention" xml:space="preserve">
    <value>Attention</value>
  </data>
  <data name="profil_acheteur_non_valide" xml:space="preserve">
    <value>Profil acheteur non valide</value>
  </data>
  <data name="erreur_chargement_donnees" xml:space="preserve">
    <value>Erreur lors du chargement des données: {0}</value>
  </data>
  <data name="erreur_chargement_coupons" xml:space="preserve">
    <value>Erreur lors du chargement des coupons : {0}</value>
  </data>
  <data name="erreur_exportation_coupons" xml:space="preserve">
    <value>Erreur lors de l'exportation des coupons : {0}</value>
  </data>
  <data name="coupon_export_success" xml:space="preserve">
    <value>"Les coupons de {0} ont été exportés avec succès.</value>
  </data>
  <data name="aucun_coupon_exporter" xml:space="preserve">
    <value>Aucun coupon à exporter pour {0}. </value>
  </data>
  <data name="erreur_telechargement_fichier" xml:space="preserve">
    <value>Erreur lors du téléchargement du fichier : {0}</value>
  </data>
  <data name="nombre_coupons_range" xml:space="preserve">
    <value>Le nombre de coupons</value>
  </data>
  <data name="nombre_coupons_required" xml:space="preserve">
    <value>Le nombre de coupons est requis.</value>
  </data>
  <data name="date_debut_required" xml:space="preserve">
    <value>La date de début est obligatoire.</value>
  </data>
  <data name="date_expiration_required" xml:space="preserve">
    <value>La date d'expiration est obligatoire</value>
  </data>
  <data name="config_keyuniquecodepromo_missing" xml:space="preserve">
    <value>La clé de configuration 'keyUniqueCodePromo' est manquante ou vide</value>
  </data>
  <data name="coupon_list_no_used_coupons" xml:space="preserve">
    <value>Liste des coupons – Aucun coupon utilisé</value>
  </data>
  <data name="coupon_status_used" xml:space="preserve">
    <value>Statut du coupon utilisé</value>
  </data>
  <data name="tab_used_coupons" xml:space="preserve">
    <value>Coupons utilisés</value>
  </data>
  <data name="coupon_list_commande_id" xml:space="preserve">
    <value>ID de commande</value>
  </data>
  <data name="coupon_list_used_date" xml:space="preserve">
    <value> Date d'utilisation</value>
  </data>
  <data name="modal_prefix" xml:space="preserve">
    <value>Préfixe</value>
  </data>
  <data name="modal_example_code" xml:space="preserve">
    <value>Exemple de code</value>
  </data>
  <data name="modal_format_explanation" xml:space="preserve">
    <value>Format : préfixe + 2 chiffres + 3 lettres</value>
  </data>
  <data name="coupon_list_used" xml:space="preserve">
    <value>Coupon utilisé</value>
  </data>
  <data name="modal_prefix_min_length" xml:space="preserve">
    <value>Le préfixe doit contenir au moins 2 caractères</value>
  </data>
  <data name="service-inclusion-exclusion" xml:space="preserve">
    <value>Inclusion/Exclusion</value>
  </data>
  <data name="services_inclusion_exclusion" xml:space="preserve">
    <value>Services d'inclusion et d'exclusion</value>
  </data>
  <data name="services_inclusion_exclusion_info" xml:space="preserve">
    <value>Gérez les structures incluses et exclues pour les différents services en environnements PREPROD et PROD.</value>
  </data>
  <data name="creation_commande" xml:space="preserve">
    <value>Création de commande</value>
  </data>
  <data name="edition_commande" xml:space="preserve">
    <value>Édition de commande</value>
  </data>
  <data name="paiement_commande" xml:space="preserve">
    <value>Paiement de commande</value>
  </data>
  <data name="envoi_mail" xml:space="preserve">
    <value>Envoi de mail</value>
  </data>
  <data name="preprod_environment" xml:space="preserve">
    <value>Environnement PREPROD</value>
  </data>
  <data name="prod_environment" xml:space="preserve">
    <value>Environnement PROD</value>
  </data>
  <data name="structure_id" xml:space="preserve">
    <value>ID de structure</value>
  </data>
  <data name="enter_structure_id" xml:space="preserve">
    <value>Entrez l'ID de structure</value>
  </data>
  <data name="inclusion" xml:space="preserve">
    <value>Inclusion</value>
  </data>
  <data name="exclusion" xml:space="preserve">
    <value>Exclusion</value>
  </data>
  <data name="add_structure" xml:space="preserve">
    <value>Ajouter la structure</value>
  </data>
  <data name="inclusions" xml:space="preserve">
    <value>Inclusions</value>
  </data>
  <data name="exclusions" xml:space="preserve">
    <value>Exclusions</value>
  </data>
  <data name="no_structures" xml:space="preserve">
    <value>Aucune structure</value>
  </data>
  <data name="common_warning" xml:space="preserve">
    <value>Avertissement</value>
  </data>
  <data name="common_info" xml:space="preserve">
    <value>Information</value>
  </data>
  <data name="structure_added_successfully" xml:space="preserve">
    <value>Struktur erfolgreich hinzugefügt</value>
  </data>
  <data name="structure_already_exists" xml:space="preserve">
    <value>La structure existe déjà</value>
  </data>
  <data name="structure_removed_successfully" xml:space="preserve">
    <value>Structure supprimée avec succès</value>
  </data>
  <data name="structure_not_found" xml:space="preserve">
    <value>Structure non trouvée</value>
  </data>
  <data name="file_does_not_exist" xml:space="preserve">
    <value>Le fichier n'existe pas</value>
  </data>
  <data name="no_permission_write" xml:space="preserve">
    <value>Vous n'avez pas les permissions d'écriture</value>
  </data>
  <data name="selected_structure_status" xml:space="preserve">
    <value>Statut de la structure sélectionnée</value>
  </data>
  <data name="select_structure" xml:space="preserve">
    <value>Sélectionner une structure</value>
  </data>
  <data name="select_structure_first" xml:space="preserve">
    <value>Sélectionnez d'abord une structure</value>
  </data>
  <data name="select_structure_option" xml:space="preserve">
    <value>Choisir une structure</value>
  </data>
  <data name="service_inclusion_exclusion_blocked" xml:space="preserve">
    <value>Bloqué</value>
  </data>
  <data name="service_inclusion_exlusion_blocked_status" xml:space="preserve">
    <value>Statut de blocage</value>
  </data>
  <data name="structure_status_recap" xml:space="preserve">
    <value>Récapitulatif de l'état de la structure</value>
  </data>
  <data name="service_inclusion_exclusion_name" xml:space="preserve">
    <value>Nom du service</value>
  </data>
  <data name="service_blocked_status" xml:space="preserve">
    <value>État de blocage</value>
  </data>
  <data name="structure_included" xml:space="preserve">
    <value>Structure incluse</value>
  </data>
  <data name="structure_excluded" xml:space="preserve">
    <value>Structure exclue</value>
  </data>
  <data name="structure_default_status" xml:space="preserve">
    <value>État par défaut</value>
  </data>
  <data name="not_blocked" xml:space="preserve">
    <value>Non bloqué</value>
  </data>
  <data name="structure_selected" xml:space="preserve">
    <value>Structure sélectionnée</value>
  </data>
  <data name="structure_removed_from_exclusion_prod" xml:space="preserve">
    <value> Structure {0} supprimée de exclusionsStructures.xml PROD</value>
  </data>
  <data name="structure_removed_from_inclusion_preprod" xml:space="preserve">
    <value>Structure {0} supprimée de inclusionsStructures.xml PREPROD</value>
  </data>
  <data name="structure_added_to_exclusion_prod" xml:space="preserve">
    <value>Structure {0} ajoutée dans exclusionsStructures.xml PROD</value>
  </data>
  <data name="structure_added_to_inclusion_preprod" xml:space="preserve">
    <value> Structure {0} ajoutée dans inclusionsStructures.xml PREPROD</value>
  </data>
  <data name="error_generic" xml:space="preserve">
    <value> Erreur : {0}</value>
  </data>
  <data name="structure_no_data_found" xml:space="preserve">
    <value>Aucune structure n'a été trouvée dans la base de données.</value>
  </data>
  <data name="structure_loading_error" xml:space="preserve">
    <value>Erreur lors du chargement des structures : {0}</value>
  </data>
  <data name="xml_loading_error_for_service" xml:space="preserve">
    <value>Erreur lors du chargement des fichiers XML pour le service {0} : {1}</value>
  </data>
  <data name="structure_name" xml:space="preserve">
    <value>Nom de la structure</value>
  </data>
  <data name="transfere-pointage-photo" xml:space="preserve">
    <value>Transfère pointage photo</value>
  </data>
  <data name="transfert_pointagephoto_titre_page" xml:space="preserve">
    <value>Pointage photo {0}{1}</value>
  </data>
  <data name="transfert_pointagephoto_label_source" xml:space="preserve">
    <value>Lieu physique source (avec image):</value>
  </data>
  <data name="transfert_pointagephoto_option_source_defaut" xml:space="preserve">
    <value>-- Sélectionnez un lieu physique source --</value>
  </data>
  <data name="transfert_pointagephoto_label_cible" xml:space="preserve">
    <value> Lieu physique cible:</value>
  </data>
  <data name="transfert_pointagephoto_option_cible_defaut" xml:space="preserve">
    <value> -- Sélectionnez un lieu physique cible --</value>
  </data>
  <data name="transfert_pointagephoto_bouton_transferer" xml:space="preserve">
    <value> Transférer</value>
  </data>
  <data name="transfert_pointagephoto_bouton_traitement" xml:space="preserve">
    <value> Traitement...</value>
  </data>
  <data name="transfert_pointagephoto_bouton_retour" xml:space="preserve">
    <value> Retour</value>
  </data>
  <data name="transfert_pointagephoto_label_chargement" xml:space="preserve">
    <value>Chargement...</value>
  </data>
  <data name="transfert_pointagephoto_erreur_chargement" xml:space="preserve">
    <value> Une erreur s'est produite lors du chargement des lieux physiques : {0}</value>
  </data>
  <data name="transfert_pointagephoto_erreur_source" xml:space="preserve">
    <value>Veuillez sélectionner un lieu physique source.</value>
  </data>
  <data name="transfert_pointagephoto_erreur_cible" xml:space="preserve">
    <value> Veuillez sélectionner un lieu physique cible.</value>
  </data>
  <data name="transfert_pointagephoto_succes_transfert" xml:space="preserve">
    <value> Le transfert de pointage photo a été effectué avec succès du lieu physique {0} vers le lieu physique {1}.</value>
  </data>
  <data name="transfert_pointagephoto_echec_transfert" xml:space="preserve">
    <value>Le transfert a échoué. Vérifiez que le lieu physique source possède bien une image.</value>
  </data>
  <data name="transfert_pointagephoto_exception_transfert" xml:space="preserve">
    <value>Une erreur s'est produite lors du transfert : {0}</value>
  </data>
  <data name="transfert_pointagephoto_succes_chargement" xml:space="preserve">
    <value>Données chargés avec  succès</value>
  </data>
  <data name="button.generate_xml_file.label" xml:space="preserve">
    <value>Générer le fichier XML personnalisé</value>
  </data>
  <data name="button.generate_xml_file.loading" xml:space="preserve">
    <value>Génération du fichier XML personnalisé...</value>
  </data>
  <data name="xml_generation_environment_undefined" xml:space="preserve">
    <value>L'environnement n'est pas défini dans la configuration.</value>
  </data>
  <data name="xml_generation_success" xml:space="preserve">
    <value>Fichier XML généré avec succès.</value>
  </data>
  <data name="modal_format" xml:space="preserve">
    <value>Format</value>
  </data>
  <data name="xml_generation_error" xml:space="preserve">
    <value>Erreur lors de la génération du fichier XML : {0}</value>
  </data>
  <data name="xml_generation_structureid_null" xml:space="preserve">
    <value>StructureId est null, génération impossible.</value>
  </data>
  <data name="transfert_vignette_titre_section" xml:space="preserve">
    <value>Transfert de vignettes</value>
  </data>
  <data name="transfert_vignette_label_source" xml:space="preserve">
    <value>Lieu physique source</value>
  </data>
  <data name="transfert_vignette_option_source_defaut" xml:space="preserve">
    <value>Sélectionnez le lieu physique source</value>
  </data>
  <data name="transfert_vignette_label_cible" xml:space="preserve">
    <value>Lieu physique cible</value>
  </data>
  <data name="transfert_vignette_option_cible_defaut" xml:space="preserve">
    <value>Sélectionnez le lieu physique cible</value>
  </data>
  <data name="transfert_vignette_bouton_transferer" xml:space="preserve">
    <value>Transférer les vignettes</value>
  </data>
  <data name="transfert_vignette_bouton_traitement" xml:space="preserve">
    <value>Traitement en cours...</value>
  </data>
  <data name="transfert_pointagephoto_titre_section" xml:space="preserve">
    <value>Transfert pointage photo sur (x,y)</value>
  </data>
  <data name="modal_format_help" xml:space="preserve">
    <value>Aide sur le format</value>
  </data>
  <data name="modal_example_code_help" xml:space="preserve">
    <value>Aide sur l'exemple de code</value>
  </data>
  <data name="couponstatus_active" xml:space="preserve">
    <value>Actif</value>
  </data>
  <data name="couponstatus_expired" xml:space="preserve">
    <value>Expiré</value>
  </data>
  <data name="couponstatus_used" xml:space="preserve">
    <value>Utilisé</value>
  </data>
  <data name="couponstatus_undefined" xml:space="preserve">
    <value>Non défini</value>
  </data>
  <data name="search_off_no_translation_found_title" xml:space="preserve">
    <value>Aucune traduction trouvée</value>
  </data>
  <data name="search_off_try_changing_search_criteria_message" xml:space="preserve">
    <value>Essayez de modifier vos critères de recherche.</value>
  </data>
  <data name="search_off_missing_translation_notice" xml:space="preserve">
    <value>Traduction manquante</value>
  </data>
  <data name="voir_champs_vides_langue" xml:space="preserve">
    <value>Voir les champs vides de la langue</value>
  </data>
  <data name="modal.max_possible_number" xml:space="preserve">
    <value>Nombre maximum possible :</value>
  </data>
  <data name="modal.warning_too_many_coupons" xml:space="preserve">
    <value>Le nombre maximum de coupons possibles dépasse 2000. Si vous générez plus de 2000 coupons, cela prendra plus de temps.
Nous vous recommandons de générer uniquement 2000 coupons à la fois.</value>
  </data>
  <data name="modal.confirm_generation_more_than_2000" xml:space="preserve">
    <value>Vous avez demandé de générer plus de 2000 coupons. La génération peut prendre plus de temps. Voulez-vous continuer ?</value>
  </data>
  <data name="toast.adjustment_done" xml:space="preserve">
    <value>Ajustement effectué</value>
  </data>
  <data name="toast.coupons_reduced_to_2000" xml:space="preserve">
    <value>Le nombre de coupons a été réduit à 2000.</value>
  </data>
  <data name="toast.number_adjusted" xml:space="preserve">
    <value>Nombre ajusté</value>
  </data>
  <data name="toast.coupons_reduced_to_x" xml:space="preserve">
    <value>Le nombre de coupons a été réduit à {0}.</value>
  </data>
  <data name="transfert_pointagephoto_bouton_verifier" xml:space="preserve">
    <value>Vérifier</value>
  </data>
  <data name="transfert_pointagephoto_bouton_generer_xml" xml:space="preserve">
    <value>Générer le fichier XML personnalisé</value>
  </data>
  <data name="transfert_resultats_incoherences_titre" xml:space="preserve">
    <value>Incohérences détectées :</value>
  </data>
  <data name="transfert_resultats_source_unique" xml:space="preserve">
    <value>présent dans la source mais pas dans la cible</value>
  </data>
  <data name="transfert_resultats_cible_unique" xml:space="preserve">
    <value>présent dans la cible mais pas dans la source</value>
  </data>
  <data name="transfert_resultats_plus_elements" xml:space="preserve">
    <value>+ {0} autres éléments non affichés.</value>
  </data>
  <data name="transfert_vignette_bouton_verifier" xml:space="preserve">
    <value>Vérifier</value>
  </data>
  <data name="transfert_vignette_bouton_generer_xml" xml:space="preserve">
    <value>Générer le fichier XML vignette</value>
  </data>
  <data name="transfert_resultats_identiques_vignette" xml:space="preserve">
    <value>Les configurations des lieux physiques sont identiques.</value>
  </data>
  <data name="transfert_pointagephoto_verification_selection_lieux" xml:space="preserve">
    <value>Sélectionnez un lieu source et un lieu cible pour vérifier.</value>
  </data>
  <data name="transfert_pointagephoto_verification_incoherences_detectees" xml:space="preserve">
    <value>Des incohérences ont été détectées.</value>
  </data>
  <data name="transfert_pointagephoto_verification_compatibles" xml:space="preserve">
    <value>Les lieux physiques sont compatibles.</value>
  </data>
  <data name="transfert_pointagephoto_verification_erreur" xml:space="preserve">
    <value>Erreur lors de la vérification</value>
  </data>
  <data name="transfert_pointagephoto_transfert_incoherences_a_corriger" xml:space="preserve">
    <value>Corrigez les incohérences avant de transférer.</value>
  </data>
  <data name="transfert_pointagephoto_transfert_selection_lieux" xml:space="preserve">
    <value>Sélectionnez un lieu source et un lieu cible.</value>
  </data>
  <data name="transfert_pointagephoto_export_structure_id_manquant" xml:space="preserve">
    <value>Structure ID manquant.</value>
  </data>
  <data name="transfert_pointagephoto_export_environnement_indefini" xml:space="preserve">
    <value>Environnement non défini.</value>
  </data>
  <data name="transfert_pointagephoto_export_xml_succes" xml:space="preserve">
    <value>Fichier XML généré avec succès. </value>
  </data>
  <data name="transfert_pointagephoto_export_xml_erreur" xml:space="preserve">
    <value>Erreur lors de la génération du fichier XML : {0}</value>
  </data>
  <data name="transfert_pointagephoto_export_salle_structure_id_manquant" xml:space="preserve">
    <value>Structure ID manquant.</value>
  </data>
  <data name="transfert_pointagephoto_export_salle_environnement_indefini" xml:space="preserve">
    <value>Environnement non défini.</value>
  </data>
  <data name="transfert_pointagephoto_export_salle_xml_succes" xml:space="preserve">
    <value>Fichier XML généré avec succès.</value>
  </data>
  <data name="transfert_pointagephoto_export_salle_xml_erreur" xml:space="preserve">
    <value>Erreur lors de la génération du fichier XML : {0}</value>
  </data>
  <data name="transfert_pointagephoto_incoherence_source" xml:space="preserve">
    <value>Rang {0}, siège {1} — présent dans la source mais pas dans la cible</value>
  </data>
  <data name="transfert_pointagephoto_incoherence_source_autres" xml:space="preserve">
    <value>+ {0} autres éléments non affichés.</value>
  </data>
  <data name="transfert_pointagephoto_incoherence_cible" xml:space="preserve">
    <value>Rang {0}, siège {1} — présent dans la cible mais pas dans la source</value>
  </data>
  <data name="Rang {0}, siège {1} — présent dans la cible mais pas dans la source" xml:space="preserve">
    <value>+ {0} autres éléments non affichés.</value>
  </data>
  <data name="transfert_pointagephoto_incoherence_cible_autres" xml:space="preserve">
    <value>+ {0} autres éléments non affichés.</value>
  </data>
  <data name="rolemanagement_edit_success_title" xml:space="preserve">
    <value>Modification réussie</value>
  </data>
  <data name="rolemanagement_edit_success_message" xml:space="preserve">
    <value>Le module a été modifié avec succès.</value>
  </data>
  <data name="rolemanagement_edit_error_title" xml:space="preserve">
    <value>Erreur lors de la modification</value>
  </data>
  <data name="rolemanagement_edit_error_message" xml:space="preserve">
    <value>Une erreur s’est produite lors de la modification : {0}</value>
  </data>
  <data name="rolemanagement_delete_success_title" xml:space="preserve">
    <value> Suppression réussie</value>
  </data>
  <data name="rolemanagement_delete_success_messag" xml:space="preserve">
    <value>Das Modul wurde erfolgreich gelöscht.</value>
  </data>
  <data name="rolemanagement_delete_error_title" xml:space="preserve">
    <value>Erreur lors de la suppression</value>
  </data>
  <data name="rolemanagement_delete_error_message" xml:space="preserve">
    <value>Beim Löschen ist ein Fehler aufgetreten: {0}</value>
  </data>
  <data name="rolemanagement_actions" xml:space="preserve">
    <value>Actions</value>
  </data>
  <data name="rolemanagement_edit" xml:space="preserve">
    <value>Modifier le module</value>
  </data>
  <data name="rolemanagement_delete" xml:space="preserve">
    <value>Supprimer</value>
  </data>
  <data name="rolemanagement_module_name" xml:space="preserve">
    <value>Nom du module</value>
  </data>
  <data name="rolemanagement_module_comment" xml:space="preserve">
    <value>Commentaire du module</value>
  </data>
  <data name="rolemanagement_save" xml:space="preserve">
    <value>Enregistrer</value>
  </data>
  <data name="rolemanagement_cancel" xml:space="preserve">
    <value>Annuler</value>
  </data>
  <data name="home_outil_gesion_tst" xml:space="preserve">
    <value> Accédez rapidement aux fonctionnalités essentielles de votre plateforme</value>
  </data>
  <data name="home_btn_primary_acceder" xml:space="preserve">
    <value>Accéder</value>
  </data>
  <data name="home_worflow_questions" xml:space="preserve">
    <value>Prêt à optimiser votre workflow ?</value>
  </data>
  <data name="home_explorer_fonctionnalite" xml:space="preserve">
    <value>Explorez les fonctionnalités disponibles ci-dessus pour commencer à améliorer votre productivité dès maintenant.</value>
  </data>
  <data name="cliquez_sur_les_cartes_pour_naviguer" xml:space="preserve">
    <value>Cliquez sur les cartes pour naviguer</value>
  </data>
  <data name="stats_support_continu" xml:space="preserve">
    <value> Support Continu</value>
  </data>
  <data name="stats_productivite" xml:space="preserve">
    <value>Productivité</value>
  </data>
  <data name="stats_optimise" xml:space="preserve">
    <value> Optimisé</value>
  </data>
  <data name="stats_possibilites" xml:space="preserve">
    <value>Possibilités</value>
  </data>
  <data name="gestion-maquette-abo-fermer" xml:space="preserve">
    <value>Gestion  maquette (Abo)</value>
  </data>
  <data name="gestionmaquette_title" xml:space="preserve">
    <value>Gestion maquette pour Abo fermer</value>
  </data>
  <data name="gestionmaquette_loading" xml:space="preserve">
    <value>Chargement...</value>
  </data>
  <data name="gestionmaquette_subscription_formule" xml:space="preserve">
    <value>Formule d'abonnement</value>
  </data>
  <data name="gestionmaquette_select_formula" xml:space="preserve">
    <value>Sélectionnez une formule</value>
  </data>
  <data name="gestionmaquette_session" xml:space="preserve">
    <value>Séance</value>
  </data>
  <data name="gestionmaquette_select_session" xml:space="preserve">
    <value>Sélectionnez une séance</value>
  </data>
  <data name="gestionmaquette_template" xml:space="preserve">
    <value>Maquette</value>
  </data>
  <data name="gestionmaquette_select_template" xml:space="preserve">
    <value>Sélectionnez une maquette</value>
  </data>
  <data name="gestionmaquette_save" xml:space="preserve">
    <value>Enregistrer</value>
  </data>
  <data name="gestionmaquette_existing_associations" xml:space="preserve">
    <value>Associations existantes</value>
  </data>
  <data name="gestionmaquette_refresh" xml:space="preserve">
    <value>Rafraîchir</value>
  </data>
  <data name="gestionmaquette_no_associations_found" xml:space="preserve">
    <value>Aucune association trouvée</value>
  </data>
  <data name="gestionmaquette_formule" xml:space="preserve">
    <value>Formule</value>
  </data>
  <data name="gestionmaquette_operation_date" xml:space="preserve">
    <value>Date opération</value>
  </data>
  <data name="gestionmaquette_actions" xml:space="preserve">
    <value>Actions</value>
  </data>
  <data name="gestionmaquetteabofermer_message_error_loading_data" xml:space="preserve">
    <value>Erreur lors du chargement des données</value>
  </data>
  <data name="gestionmaquetteabofermer_message_error_loading_associations" xml:space="preserve">
    <value>Erreur lors du chargement des associations</value>
  </data>
  <data name="gestionmaquetteabofermer_message_please_select_all" xml:space="preserve">
    <value>Veuillez sélectionner tous les éléments</value>
  </data>
  <data name="gestionmaquetteabofermer_message_association_already_exists" xml:space="preserve">
    <value>Cette association existe déjà</value>
  </data>
  <data name="gestionmaquetteabofermer_message_association_saved_successfully" xml:space="preserve">
    <value>Association enregistrée avec succès</value>
  </data>
  <data name="gestionmaquetteabofermer_message_error_saving_association" xml:space="preserve">
    <value>Erreur lors de l’enregistrement de l’association</value>
  </data>
  <data name="gestionmaquetteabofermer_message_association_deleted_successfully" xml:space="preserve">
    <value>Association supprimée avec succès</value>
  </data>
  <data name="gestionmaquetteabofermer_message_error_deleting_association" xml:space="preserve">
    <value>Erreur lors de la suppression de l’association</value>
  </data>
  <data name="gestionmaquetteabofermer_message_error" xml:space="preserve">
    <value>Erreur</value>
  </data>
  <data name="gestionmaquette_new_association" xml:space="preserve">
    <value>Nouvelle liaison maquette - séance - abonnement</value>
  </data>
  <data name="preparation-mise-vente" xml:space="preserve">
    <value>Prépa mise en vente</value>
  </data>
  <data name="clean_tables_success_message" xml:space="preserve">
    <value>Les tables ont été nettoyées avec succès. Toutes les données des tables queuing ont été supprimées.</value>
  </data>
  <data name="clean_tables_failed_message" xml:space="preserve">
    <value>Le nettoyage n'a pas pu être effectué. Des données récentes sont présentes.</value>
  </data>
  <data name="clean_tables_error_message" xml:space="preserve">
    <value>Erreur lors du nettoyage : {0}</value>
  </data>
  <data name="check_cleanup_no_recent_data_message" xml:space="preserve">
    <value>Aucun passage récent détecté. Le nettoyage peut être effectué en toute sécurité.</value>
  </data>
  <data name="check_cleanup_recent_data_warning" xml:space="preserve">
    <value>Attention : des données récentes (moins d'1 heure) ont été détectées. Le nettoyage n'est pas possible pour des raisons de sécurité.</value>
  </data>
  <data name="check_cleanup_error_message" xml:space="preserve">
    <value>Erreur lors de la vérification : {0}</value>
  </data>
  <data name="on_initialized_click_to_verify_message" xml:space="preserve">
    <value>Cliquez sur 'Vérifier à nouveau' pour contrôler l'état des passages avant le nettoyage.</value>
  </data>
  <data name="preparation_mise_vente_title" xml:space="preserve">
    <value>Préparation à la mise en vente</value>
  </data>
  <data name="preparation_mise_vente_structure_label" xml:space="preserve">
    <value>Structure : {0} ({1})</value>
  </data>
  <data name="preparation_mise_vente_loading" xml:space="preserve">
    <value>Vérification des données en cours...</value>
  </data>
  <data name="preparation_mise_vente_success_message" xml:space="preserve">
    <value>Les tables ont été nettoyées avec succès. Toutes les données des tables queuing ont été supprimées.</value>
  </data>
  <data name="preparation_mise_vente_error_message" xml:space="preserve">
    <value>Le nettoyage n'a pas pu être effectué. Des données récentes sont présentes.</value>
  </data>
  <data name="preparation_mise_vente_check_error" xml:space="preserve">
    <value> Erreur lors de la vérification : {0}</value>
  </data>
  <data name="preparation_mise_vente_check_success" xml:space="preserve">
    <value>Aucun passage récent détecté. Le nettoyage peut être effectué en toute sécurité.</value>
  </data>
  <data name="preparation_mise_vente_check_warning" xml:space="preserve">
    <value>Attention : des données récentes (moins d'1 heure) ont été détectées. Le nettoyage n'est pas possible.</value>
  </data>
  <data name="preparation_mise_vente_click_verify" xml:space="preserve">
    <value>Cliquez sur 'Vérifier à nouveau' pour contrôler l'état des données avant le nettoyage.</value>
  </data>
  <data name="preparation_mise_vente_verification_status" xml:space="preserve">
    <value>État de vérification</value>
  </data>
  <data name="preparation_mise_vente_not_verified" xml:space="preserve">
    <value>État non vérifié</value>
  </data>
  <data name="preparation_mise_vente_not_verified_description" xml:space="preserve">
    <value>L'état des passages n'a pas encore été vérifié.</value>
  </data>
  <data name="preparation_mise_vente_not_verified_hint" xml:space="preserve">
    <value>Cliquez sur 'Vérifier à nouveau' pour contrôler les données.</value>
  </data>
  <data name="preparation_mise_vente_recent_data" xml:space="preserve">
    <value>Passages récents détectés</value>
  </data>
  <data name="preparation_mise_vente_recent_data_description" xml:space="preserve">
    <value>Des passages de moins d'une heure sont présents dans la table queuing</value>
  </data>
  <data name="preparation_mise_vente_recent_data_hint" xml:space="preserve">
    <value>Le nettoyage est actuellement bloqué pour des raisons de sécurité.</value>
  </data>
  <data name="preparation_mise_vente_ready_to_cleanup" xml:space="preserve">
    <value>Prêt pour le nettoyage</value>
  </data>
  <data name="preparation_mise_vente_no_recent_data" xml:space="preserve">
    <value>Aucun passage récent détecté.</value>
  </data>
  <data name="preparation_mise_vente_safe_to_cleanup" xml:space="preserve">
    <value>Le nettoyage des tables peut être effectué en toute sécurité.</value>
  </data>
  <data name="preparation_mise_vente_actions" xml:space="preserve">
    <value>Actions disponibles</value>
  </data>
  <data name="preparation_mise_vente_check_button" xml:space="preserve">
    <value>Vérifier à nouveau</value>
  </data>
  <data name="preparation_mise_vente_check_button_hin" xml:space="preserve">
    <value>Actualise l'état de vérification</value>
  </data>
  <data name="preparation_mise_vente_cleanup_required" xml:space="preserve">
    <value>Vérification requise</value>
  </data>
  <data name="preparation_mise_vente_cleanup_blocked" xml:space="preserve">
    <value>Nettoyage bloqué</value>
  </data>
  <data name="preparation_mise_vente_cleanup_button" xml:space="preserve">
    <value>Nettoyer les tables</value>
  </data>
  <data name="preparation_mise_vente_cleanup_button_hint" xml:space="preserve">
    <value>Supprime toutes les données des tables queuing</value>
  </data>
  <data name="preparation_mise_vente_cleanup_warning" xml:space="preserve">
    <value>Le nettoyage supprimera toutes les données des tables queuing_{0} et queuing_{0}_histopassages.</value>
  </data>
  <data name="preparation_mise_vente_cleanup_irreversible" xml:space="preserve">
    <value>Attendez que les passages récents aient plus d'une heure ou que les utilisateurs ne soient plus connectés.</value>
  </data>
  <data name="preparation_mise_vente_verify_first" xml:space="preserve">
    <value>Veuillez d'abord vérifier l'état des passages avant de procéder au nettoyage.</value>
  </data>
  <data name="preparation_mise_vente_verify_purpose" xml:space="preserve">
    <value>Cette vérification permet de s'assurer qu'aucune donnée récente ne sera perdue.</value>
  </data>
  <data name="preparation_mise_vente_check_button_hint" xml:space="preserve">
    <value>Actualise l'état de vérification</value>
  </data>
  <data name="preparation_mise_vente_cancel_button" xml:space="preserve">
    <value>Annuler</value>
  </data>
  <data name="preparation_mise_vente_cleanup_confirmation_title" xml:space="preserve">
    <value>Confirmation du nettoyage</value>
  </data>
  <data name="preparation_mise_vente_advice_wait_or_disconnect" xml:space="preserve">
    <value>Attendez que les passages récents aient plus d'une heure.</value>
  </data>
  <data name="tst_tool_configuration_name" xml:space="preserve">
    <value>Configuration</value>
  </data>
  <data name="tst_tool_configuration_description" xml:space="preserve">
    <value>Gestion des paramètres et configurations</value>
  </data>
  <data name="tst_tool_commerce_name" xml:space="preserve">
    <value> Gestion commerciale</value>
  </data>
  <data name="tst_tool_commerce_description" xml:space="preserve">
    <value>Outils de gestion commerciale</value>
  </data>
  <data name="tst_tool_roles_name" xml:space="preserve">
    <value> Gestion rôles</value>
  </data>
  <data name="tst_tool_roles_description" xml:space="preserve">
    <value>Gestion des rôles</value>
  </data>
  <data name="tst_tool_partners_name" xml:space="preserve">
    <value>Gestion des partenaires</value>
  </data>
  <data name="tst_tool_partners_description" xml:space="preserve">
    <value>Gestion des partenaires</value>
  </data>
  <data name="tst_tool_traductions_name" xml:space="preserve">
    <value> Traductions</value>
  </data>
  <data name="tst_tool_traductions_description" xml:space="preserve">
    <value>Gestion des traductions</value>
  </data>
  <data name="tst_tool_monitoring_name" xml:space="preserve">
    <value>Monitoring</value>
  </data>
  <data name="tst_tool_monitoring_description" xml:space="preserve">
    <value>Surveillance et traçage</value>
  </data>
  <data name="tst_tool_securite_name" xml:space="preserve">
    <value>Sécurité</value>
  </data>
  <data name="tst_tool_securite_description" xml:space="preserve">
    <value>Gestion de la sécurité</value>
  </data>
  <data name="common_voir_modules_label" xml:space="preserve">
    <value>Voir les modules</value>
  </data>
  <data name="common_survolez_outils_pour_voir_modules" xml:space="preserve">
    <value> Survolez les outils pour voir les modules disponibles</value>
  </data>
  <data name="loading_spinner_visually_hidden_text" xml:space="preserve">
    <value>Chargement...</value>
  </data>
  <data name="loading_spinner_message_text" xml:space="preserve">
    <value>Chargement des données...</value>
  </data>
  <data name="pagination_items_per_page_label" xml:space="preserve">
    <value> Éléments par page</value>
  </data>
  <data name="pagination_showing_items_range" xml:space="preserve">
    <value>Affichage des éléments {0} à {1} sur {2}</value>
  </data>
  <data name="pagination_navigation_label" xml:space="preserve">
    <value> Navigation de pagination</value>
  </data>
  <data name="pagination_button_previous" xml:space="preserve">
    <value> Précédent</value>
  </data>
  <data name="pagination_button_next" xml:space="preserve">
    <value> Suivant</value>
  </data>
  <data name="pagination_page_info_text" xml:space="preserve">
    <value>Page {0} sur {1}</value>
  </data>
  <data name="toast_message_data_loaded_success" xml:space="preserve">
    <value>Les données ont été chargées avec succès</value>
  </data>
  <data name="widget-cross-selling" xml:space="preserve">
    <value>Cross selling</value>
  </data>
  <data name="widget-waiting-list" xml:space="preserve">
    <value>Config. listes d’attente</value>
  </data>
  <data name="widget-catalogue-offre" xml:space="preserve">
    <value>Catalogues d'offres</value>
  </data>
  <data name="liste_catalogues_offres" xml:space="preserve">
    <value>Liste de catalogues d'offres</value>
  </data>
  <data name="configuration_listes_attente" xml:space="preserve">
    <value> Configuration des listes d’attente</value>
  </data>
  <data name="gestion_widgets" xml:space="preserve">
    <value>Gestion des widgets</value>
  </data>
  <data name="description_gestion_widgets" xml:space="preserve">
    <value>Gère l'affichage et l'ordre des widgets.</value>
  </data>
  <data name=" liste-structure-with-partenaires" xml:space="preserve">
    <value>Liste des structures et leurs partenaires</value>
  </data>
  <data name="structure_partners_title" xml:space="preserve">
    <value>Partenaires de la Structure</value>
  </data>
  <data name="selected_structures_partners_title" xml:space="preserve">
    <value>Structures Sélectionnées &amp; Partenaires</value>
  </data>
  <data name="structures_partners_title" xml:space="preserve">
    <value>Structures &amp; Partenaires</value>
  </data>
  <data name="structure_associated_partners" xml:space="preserve">
    <value>Partenaires associés à cette structure</value>
  </data>
  <data name="selected_structures_count" xml:space="preserve">
    <value>structure(s) sélectionnée(s)</value>
  </data>
  <data name="from_partners_page" xml:space="preserve">
    <value>Depuis la page des partenaires</value>
  </data>
  <data name="structures_partners_management" xml:space="preserve">
    <value>Gestion des relations entre structures et partenaires</value>
  </data>
  <data name="return_to_partners" xml:space="preserve">
    <value>Retour aux partenaires</value>
  </data>
  <data name="full_list" xml:space="preserve">
    <value>Liste complète</value>
  </data>
  <data name="structures_found" xml:space="preserve">
    <value>Structures trouvées</value>
  </data>
  <data name="total_partners" xml:space="preserve">
    <value>Total partenaires</value>
  </data>
  <data name="with_partners" xml:space="preserve">
    <value>Avec partenaires</value>
  </data>
  <data name="loading_data" xml:space="preserve">
    <value>Chargement des données...</value>
  </data>
  <data name="loading_error" xml:space="preserve">
    <value>Erreur lors du chargement :</value>
  </data>
  <data name="no_structures_found" xml:space="preserve">
    <value>Aucune structure trouvée</value>
  </data>
  <data name="search_structures" xml:space="preserve">
    <value>Rechercher des structures...</value>
  </data>
  <data name="check_database_connection" xml:space="preserve">
    <value>Vérifiez la connexion à la base de données de la structure</value>
  </data>
  <data name="database_connection_issue" xml:space="preserve">
    <value>Problème de connexion à la base de données</value>
  </data>
  <data name="structure_database_not_configured" xml:space="preserve">
    <value>La structure {0} n'a pas de connexion à la base de données configurée ou accessible.</value>
  </data>
  <data name="current_structure" xml:space="preserve">
    <value>Structure actuelle</value>
  </data>
  <data name="step_2_profils_lies_revendeur" xml:space="preserve">
    <value>Étape 2: Profils acheteurs du revendeur</value>
  </data>
  <data name="step_3_profils_structure" xml:space="preserve">
    <value>Étape 3: Profils acheteurs de la structure</value>
  </data>
  <data name="selected_profil_revendeur" xml:space="preserve">
    <value>Profil du revendeur sélectionné</value>
  </data>
  <data name="select_structure_profil_to_link" xml:space="preserve">
    <value>Sélectionnez le profil acheteur de la structure à associer</value>
  </data>
  <data name="ok_continue" xml:space="preserve">
    <value>OK - Continuer</value>
  </data>
  <data name="ok_save_liaison" xml:space="preserve">
    <value>OK - Enregistrer la liaison</value>
  </data>
  <data name="no_structure_profils_found" xml:space="preserve">
    <value>Aucun profil acheteur trouvé pour cette structure</value>
  </data>
  <data name="liaison_explanation" xml:space="preserve">
    <value>Cette liaison permettra au revendeur d'utiliser le profil acheteur de la structure sélectionné</value>
  </data>
  <data name="revendeur_profil_selected" xml:space="preserve">
    <value>Profil du revendeur</value>
  </data>
  <data name="structure_profil_to_select" xml:space="preserve">
    <value>Profil de la structure à associer</value>
  </data>
  <data name="liaison_revendeur_description_new_workflow" xml:space="preserve">
    <value>Créer des liaisons entre revendeurs et profils acheteurs : 1) Sélectionner un revendeur, 2) Choisir son profil acheteur, 3) Sélectionner la structure cible et son profil acheteur</value>
  </data>
  <data name="select_revendeur" xml:space="preserve">
    <value>Sélectionner un revendeur</value>
  </data>
  <data name="select_profil_acheteur" xml:space="preserve">
    <value>Sélectionner un profil acheteur</value>
  </data>
  <data name="select_structure_and_profil" xml:space="preserve">
    <value>Sélectionner structure et profil</value>
  </data>
  <data name="confirmation" xml:space="preserve">
    <value>Confirmation</value>
  </data>
  <data name="select_revendeur_description" xml:space="preserve">
    <value>Choisissez le revendeur pour lequel vous souhaitez créer une liaison</value>
  </data>
  <data name="revendeur_from_structure" xml:space="preserve">
    <value>Revendeur de la structure</value>
  </data>
  <data name="step_3_select_structure_and_profil" xml:space="preserve">
    <value>Étape 3: Sélectionner la structure cible et le profil acheteur</value>
  </data>
  <data name="select_target_structure_explanation" xml:space="preserve">
    <value>Sélectionnez la structure cible où vous souhaitez créer la liaison, puis choisissez le profil acheteur de cette structure</value>
  </data>
  <data name="select_target_structure" xml:space="preserve">
    <value>Sélectionner la structure cible</value>
  </data>
  <data name="no_revendeurs_database_issue" xml:space="preserve">
    <value>Aucun revendeur trouvé. Vérifiez les connexions aux bases de données des structures.</value>
  </data>
  <data name="select_structure_profil" xml:space="preserve">
    <value>Sélectionner profil structure</value>
  </data>
  <data name="step_3_select_structure_profil" xml:space="preserve">
    <value>Étape 3: Sélectionner le profil acheteur de la structure</value>
  </data>
  <data name="select_structure_profil_explanation" xml:space="preserve">
    <value>Sélectionnez le profil acheteur de la structure du revendeur à associer</value>
  </data>
  <data name="revendeur_structure" xml:space="preserve">
    <value>Structure du revendeur</value>
  </data>
  <data name="revendeur_profil_acheteur" xml:space="preserve">
    <value>Profil acheteur du revendeur</value>
  </data>
  <data name="target_structure" xml:space="preserve">
    <value>Structure cible</value>
  </data>
  <data name="structure_profil_acheteur" xml:space="preserve">
    <value>Profil acheteur de la structure</value>
  </data>
  <data name="create_new_liaison" xml:space="preserve">
    <value>Créer une nouvelle liaison</value>
  </data>
  <data name="no_profils_lies_found" xml:space="preserve">
    <value>Aucun profil lié trouvé pour ce revendeur</value>
  </data>
  <data name="no_profils_lies_explanation" xml:space="preserve">
    <value>Ce revendeur n'a pas encore de profils acheteurs liés. Vous pouvez créer une nouvelle liaison en sélectionnant un profil acheteur de la structure.</value>
  </data>
  <data name="no_profils_lies_workflow_explanation" xml:space="preserve">
    <value>Ce revendeur n'a aucun profil acheteur lié. Pour créer une liaison, vous devez d'abord avoir des profils liés existants.</value>
  </data>
  <data name="create_liaison_first_message" xml:space="preserve">
    <value>Veuillez d'abord créer des liaisons de profils acheteurs pour ce revendeur, puis revenir à ce workflow.</value>
  </data>
  <data name="back_to_revendeurs" xml:space="preserve">
    <value>Retour aux revendeurs</value>
  </data>
  <data name="ok_select_structure" xml:space="preserve">
    <value>OK - Sélectionner structure</value>
  </data>
  <data name="select_structure" xml:space="preserve">
    <value>Sélectionner structure</value>
  </data>
  <data name="step_3_select_structure" xml:space="preserve">
    <value>Étape 3: Sélectionner structure</value>
  </data>
  <data name="select_target_structure" xml:space="preserve">
    <value>Sélectionner la structure cible</value>
  </data>
  <data name="ok_load_structure_profiles" xml:space="preserve">
    <value>OK - Charger les profils de la structure</value>
  </data>
  <data name="no_structures_found" xml:space="preserve">
    <value>Aucune structure trouvée</value>
  </data>
</root>