﻿@page "/liaison-acheteurr-revendeur"
@using Core.Themis.Libraries.DTO
@using Core.Themis.Libraries.BLL.Interfaces
@using Microsoft.AspNetCore.Components

<div class="liaison-revendeur-container">
    <div class="page-header">
        <h3>@Localizer["liaison_revendeur_title"]</h3>
        <p class="page-description">@Localizer["liaison_revendeur_description_new_workflow"]</p>

        <!-- Indicateur d'étapes -->
        <div class="workflow-steps mb-4">
            <div class="step @(CurrentStep == LiaisonStep.SelectRevendeur ? "active" : CurrentStep > LiaisonStep.SelectRevendeur ? "completed" : "")">
                <span class="step-number">1</span>
                <span class="step-label">@Localizer["select_revendeur"]</span>
            </div>
            <div class="step @(CurrentStep == LiaisonStep.SelectProfilAcheteur ? "active" : CurrentStep > LiaisonStep.SelectProfilAcheteur ? "completed" : "")">
                <span class="step-number">2</span>
                <span class="step-label">@Localizer["select_profil_acheteur"]</span>
            </div>
            <div class="step @(CurrentStep == LiaisonStep.SelectStructureProfilAcheteur ? "active" : CurrentStep > LiaisonStep.SelectStructureProfilAcheteur ? "completed" : "")">
                <span class="step-number">3</span>
                <span class="step-label">@Localizer["select_structure_profil"]</span>
            </div>
            <div class="step @(CurrentStep == LiaisonStep.Confirmation ? "active" : "")">
                <span class="step-number">4</span>
                <span class="step-label">@Localizer["confirmation"]</span>
            </div>
        </div>
    </div>

    @if (IsLoading)
    {
        <div class="loading-container">
            <div class="spinner-border text-primary" role="status">
                <span class="sr-only">@Localizer["loading"]</span>
            </div>
            <p>@Localizer["loading_data"]</p>
        </div>
    }
    else
    {
        <div class="liaison-steps-container">
            <!-- Étape 1: Liste de tous les revendeurs -->
            @if (CurrentStep == LiaisonStep.SelectRevendeur)
            {
                <div class="step-container">
                    <h4>@Localizer["step_1_select_revendeur"]</h4>
                    <p class="step-description">@Localizer["select_revendeur_description"]</p>

                    <div class="revendeurs-list">
                        @if (Revendeurs?.Any() == true)
                        {
                            @foreach (var revendeur in Revendeurs)
                            {
                                <div class="revendeur-item">
                                    <div class="revendeur-info">
                                        <strong>@(revendeur.Nom ?? revendeur.Name ?? revendeur.Libelle)</strong>
                                        <div class="revendeur-details">
                                            <span class="revendeur-id">ID: @revendeur.Id</span>
                                            @if (!string.IsNullOrEmpty(revendeur.StructureName))
                                            {
                                                <span class="structure-info">Structure: @revendeur.StructureName (@revendeur.StructureId)</span>
                                            }
                                        </div>
                                    </div>
                                    <button class="btn btn-primary" @onclick="() => SelectRevendeur(revendeur)">
                                        @Localizer["select"]
                                    </button>
                                </div>
                            }
                        }
                        else
                        {
                            <div class="no-data">
                                @if (HasDatabaseConnectionIssue)
                                {
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        <strong>@Localizer["database_connection_issue"]</strong>
                                        <p>@Localizer["no_revendeurs_database_issue"]</p>
                                        <p class="text-muted">@Localizer["check_database_connection"]</p>
                                    </div>
                                }
                                else
                                {
                                    <p>@Localizer["no_revendeurs_found"]</p>
                                }
                            </div>
                        }
                    </div>
                </div>
            }

            <!-- Étape 2: Profils acheteurs liés au revendeur sélectionné -->
            @if (CurrentStep == LiaisonStep.SelectProfilAcheteur)
            {
                <div class="step-container">
                    <div class="step-header">
                        <h4>@Localizer["step_2_profils_lies_revendeur"]</h4>
                        <p>@Localizer["selected_revendeur"]: <strong>@(SelectedRevendeur?.Nom ?? SelectedRevendeur?.Name ?? SelectedRevendeur?.Libelle)</strong></p>
                        <p class="text-muted">@Localizer["revendeur_from_structure"]: @SelectedRevendeur?.StructureName (@SelectedRevendeur?.StructureId)</p>
                        <button class="btn btn-secondary btn-sm" @onclick="GoBackToRevendeurs">
                            @Localizer["back"]
                        </button>
                    </div>

                    <div class="profils-list">
                        @if (ProfilsAcheteursLies?.Any() == true)
                        {
                            <div class="profils-selection">
                                @foreach (var profil in ProfilsAcheteursLies)
                                {
                                    <div class="profil-item @(SelectedProfilAcheteur?.Id == profil.Id ? "selected" : "")">
                                        <div class="profil-info">
                                            <input type="radio" name="profilAcheteur" value="@profil.Id"
                                                   @onchange="() => SetSelectedProfilAcheteur(profil)"
                                                   checked="@(SelectedProfilAcheteur?.Id == profil.Id)" />
                                            <strong>@profil.Libelle</strong>
                                            <span class="profil-id">ID: @profil.Id</span>
                                        </div>
                                    </div>
                                }

                                @if (SelectedProfilAcheteur != null)
                                {
                                    <div class="action-buttons mt-3">
                                        <button class="btn btn-success" @onclick="ProceedToStructureSelection">
                                            @Localizer["ok_continue"]
                                        </button>
                                    </div>
                                }
                            </div>
                        }
                        else
                        {
                            <div class="no-data">
                                <div class="alert alert-info">
                                    <h5>@Localizer["no_profils_lies_found"]</h5>
                                    <p>@Localizer["no_profils_lies_explanation"]</p>
                                </div>

                                <div class="action-buttons mt-3">
                                    <button class="btn btn-success" @onclick="StartNewLink">
                                        <i class="fas fa-plus"></i> @Localizer["create_new_liaison"]
                                    </button>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            }

            <!-- Étape 3: Sélection du profil acheteur de la structure -->
            @if (CurrentStep == LiaisonStep.SelectStructureProfilAcheteur)
            {
                <div class="step-container">
                    <div class="step-header">
                        <h4>@Localizer["step_3_select_structure_profil"]</h4>
                        <div class="liaison-summary">
                            <p>@Localizer["selected_revendeur"]: <strong>@(SelectedRevendeur?.Nom ?? SelectedRevendeur?.Name ?? SelectedRevendeur?.Libelle)</strong></p>
                            <p>@Localizer["revendeur_profil_selected"]: <strong>@SelectedProfilAcheteur?.Libelle</strong></p>
                            <p>@Localizer["revendeur_structure"]: <strong>@SelectedRevendeur?.StructureName (@SelectedRevendeur?.StructureId)</strong></p>
                            <p class="text-info"><em>@Localizer["select_structure_profil_explanation"]</em></p>
                        </div>
                        <button class="btn btn-secondary btn-sm" @onclick="GoBackToProfilsAcheteurs">
                            @Localizer["back"]
                        </button>
                    </div>

                    <!-- Profils acheteurs de la structure du revendeur -->
                    <div class="profils-structure-list">
                        <h5>@Localizer["select_structure_profil_to_link"] (@SelectedRevendeur?.StructureName)</h5>
                        @if (TousProfilsAcheteurs?.Any() == true)
                        {
                            <div class="profils-selection">
                                @foreach (var profil in TousProfilsAcheteurs)
                                {
                                    <div class="profil-item @(SelectedStructureProfilAcheteur?.Id == profil.Id ? "selected" : "")">
                                        <div class="profil-info">
                                            <input type="radio" name="structureProfilAcheteur" value="@profil.Id"
                                                   @onchange="() => SetSelectedStructureProfilAcheteur(profil)"
                                                   checked="@(SelectedStructureProfilAcheteur?.Id == profil.Id)" />
                                            <strong>@profil.Libelle</strong>
                                            <span class="profil-id">ID: @profil.Id</span>
                                        </div>
                                    </div>
                                }

                                @if (SelectedStructureProfilAcheteur != null)
                                {
                                    <div class="action-buttons mt-3">
                                        <button class="btn btn-success" @onclick="SaveLiaison" disabled="@IsSaving">
                                            @if (IsSaving)
                                            {
                                                <span class="spinner-border spinner-border-sm" role="status"></span>
                                                @Localizer["saving"]
                                            }
                                            else
                                            {
                                                @Localizer["ok_save_liaison"]
                                            }
                                        </button>
                                    </div>
                                }
                            </div>
                        }
                        else
                        {
                            <div class="no-data">
                                <p>@Localizer["no_structure_profils_found"]</p>
                                <p class="text-muted">@Localizer["check_database_connection"]</p>
                            </div>
                        }
                    </div>
                </div>
            }

            <!-- Étape 4: Confirmation de sauvegarde -->
            @if (CurrentStep == LiaisonStep.Confirmation)
            {
                <div class="step-container">
                    <div class="confirmation-container">
                        <div class="alert alert-success">
                            <h4>@Localizer["liaison_saved_successfully"]</h4>
                            <p>@Localizer["liaison_details"]:</p>
                            <ul>
                                <li>@Localizer["revendeur"]: <strong>@(SelectedRevendeur?.Nom ?? SelectedRevendeur?.Name ?? SelectedRevendeur?.Libelle)</strong></li>
                                <li>@Localizer["revendeur_profil_acheteur"]: <strong>@SelectedProfilAcheteur?.Libelle</strong></li>
                                <li>@Localizer["revendeur_structure"]: <strong>@SelectedRevendeur?.StructureName (@SelectedRevendeur?.StructureId)</strong></li>
                                <li>@Localizer["structure_profil_acheteur"]: <strong>@SelectedStructureProfilAcheteur?.Libelle</strong></li>
                            </ul>
                        </div>

                        <div class="action-buttons">
                            <button class="btn btn-primary" @onclick="StartNewLiaison">
                                @Localizer["create_new_liaison"]
                            </button>
                        </div>
                    </div>
                </div>
            }
        </div>
    }

    <!-- Messages d'erreur -->
    @if (!string.IsNullOrEmpty(ErrorMessage))
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            @ErrorMessage
            <button type="button" class="btn-close" @onclick="ClearError"></button>
        </div>
    }
</div>

<style>
    .workflow-steps {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        padding: 1rem;
        background-color: #f8f9fa;
        border-radius: 8px;
    }

    .step {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        flex: 1;
    }

    .step:not(:last-child)::after {
        content: '';
        position: absolute;
        top: 15px;
        right: -50%;
        width: 100%;
        height: 2px;
        background-color: #dee2e6;
        z-index: 1;
    }

    .step.completed:not(:last-child)::after {
        background-color: #28a745;
    }

    .step-number {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: #dee2e6;
        color: #6c757d;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-bottom: 0.5rem;
        position: relative;
        z-index: 2;
    }

    .step.active .step-number {
        background-color: #007bff;
        color: white;
    }

    .step.completed .step-number {
        background-color: #28a745;
        color: white;
    }

    .step-label {
        font-size: 0.875rem;
        text-align: center;
        color: #6c757d;
    }

    .step.active .step-label {
        color: #007bff;
        font-weight: bold;
    }

    .step.completed .step-label {
        color: #28a745;
    }

    .revendeur-item, .profil-item, .structure-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        margin-bottom: 0.5rem;
        transition: all 0.2s ease;
    }

    .revendeur-item:hover, .profil-item:hover, .structure-item:hover {
        border-color: #007bff;
        background-color: #f8f9fa;
    }

    .revendeur-item.selected, .profil-item.selected, .structure-item.selected {
        border-color: #007bff;
        background-color: #e3f2fd;
    }

    .revendeur-details {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }

    .revendeur-id, .profil-id, .structure-id {
        font-size: 0.875rem;
        color: #6c757d;
    }

    .structure-info {
        font-size: 0.875rem;
        color: #28a745;
        font-weight: 500;
    }

    .liaison-summary {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1rem;
    }

    .liaison-summary p {
        margin-bottom: 0.5rem;
    }

    .structure-selection, .profils-structure-list {
        margin-top: 1rem;
    }

    .structures-list {
        max-height: 300px;
        overflow-y: auto;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 0.5rem;
    }

    .confirmation-container {
        text-align: center;
        padding: 2rem;
    }

    .action-buttons {
        margin-top: 1rem;
        text-align: center;
    }

    .step-description {
        color: #6c757d;
        margin-bottom: 1rem;
        font-style: italic;
    }
</style>
