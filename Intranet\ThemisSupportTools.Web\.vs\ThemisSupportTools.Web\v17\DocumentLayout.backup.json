{"Version": 1, "WorkspaceRootPath": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\liaisonacheteurrrevendeur\\liaisonacheteurrrevendeur.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\liaisonacheteurrrevendeur\\liaisonacheteurrrevendeur.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5A450D3A-4E7F-431C-8DAA-97F1D02D79C0}|..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Core.Themis.Libraries.Data.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\libraries\\core.themis.libraries.data\\core.themis.librairies.data\\repositories\\open\\partner\\interfaces\\irevendeurisprofilacheteuronstructurerepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5A450D3A-4E7F-431C-8DAA-97F1D02D79C0}|..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Core.Themis.Libraries.Data.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\libraries\\core.themis.libraries.data\\core.themis.librairies.data\\repositories\\open\\partner\\revendeurisprofilacheteuronstructurerepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{95E6348E-C5AA-4836-BAC6-EF0845F452F8}|..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\libraries\\core.themis.libraries.bll\\core.themis.libraries.bll\\interfaces\\ipartnermanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5A450D3A-4E7F-431C-8DAA-97F1D02D79C0}|..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Core.Themis.Libraries.Data.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\libraries\\core.themis.libraries.data\\core.themis.librairies.data\\repositories\\wsadmin\\revendeurrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5A450D3A-4E7F-431C-8DAA-97F1D02D79C0}|..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Core.Themis.Libraries.Data.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\libraries\\core.themis.libraries.data\\core.themis.librairies.data\\repositories\\lieuphysique\\lieuphysiquerepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3FCB00B9-3AC3-4E3C-A0C4-D63B67213968}|..\\..\\Libraries\\Core.Themis.Libraries.DTO\\Core.Themis.Libraries.DTO.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\libraries\\core.themis.libraries.dto\\revendeurisprofilacheteuronstructuredto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3FCB00B9-3AC3-4E3C-A0C4-D63B67213968}|..\\..\\Libraries\\Core.Themis.Libraries.DTO\\Core.Themis.Libraries.DTO.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\libraries\\core.themis.libraries.dto\\revendeurdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3FCB00B9-3AC3-4E3C-A0C4-D63B67213968}|..\\..\\Libraries\\Core.Themis.Libraries.DTO\\Core.Themis.Libraries.DTO.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\libraries\\core.themis.libraries.dto\\buyerprofildto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5A450D3A-4E7F-431C-8DAA-97F1D02D79C0}|..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Core.Themis.Libraries.Data.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\libraries\\core.themis.libraries.data\\core.themis.librairies.data\\repositories\\open\\buyerprofil\\buyerprofilrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{95E6348E-C5AA-4836-BAC6-EF0845F452F8}|..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\libraries\\core.themis.libraries.bll\\core.themis.libraries.bll\\partnermanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\liaisonacheteurrrevendeur\\liaisonacheteurrrevendeur.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\liaisonacheteurrrevendeur\\liaisonacheteurrrevendeur.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{5A450D3A-4E7F-431C-8DAA-97F1D02D79C0}|..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Core.Themis.Libraries.Data.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\libraries\\core.themis.libraries.data\\core.themis.librairies.data\\entities\\open\\partner\\revendeurisprofilacheteuronstructureentity.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{95E6348E-C5AA-4836-BAC6-EF0845F452F8}|..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\libraries\\core.themis.libraries.bll\\core.themis.libraries.bll\\extentions\\servicesbuilder\\rodrigueservicesextension.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{95E6348E-C5AA-4836-BAC6-EF0845F452F8}|..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\libraries\\core.themis.libraries.bll\\core.themis.libraries.bll\\automapperprofiles\\themissupporttoolsprofiles.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{95E6348E-C5AA-4836-BAC6-EF0845F452F8}|..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\libraries\\core.themis.libraries.bll\\core.themis.libraries.bll\\services\\access\\tstaccessservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\wwwroot\\app.css||{A5401142-F49D-43DB-90B1-F57BA349E55C}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\wwwroot\\app.css||{A5401142-F49D-43DB-90B1-F57BA349E55C}"}, {"AbsoluteMoniker": "D:0:0:{5A450D3A-4E7F-431C-8DAA-97F1D02D79C0}|..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Core.Themis.Libraries.Data.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\libraries\\core.themis.libraries.data\\core.themis.librairies.data\\repositories\\wsadmin\\partnerrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5A450D3A-4E7F-431C-8DAA-97F1D02D79C0}|..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Core.Themis.Libraries.Data.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\libraries\\core.themis.libraries.data\\core.themis.librairies.data\\repositories\\wsadmin\\interfaces\\ipartnerrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\gestionroles\\rolemanagement.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\gestionroles\\rolemanagement.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\partners\\partners.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\partners\\partners.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\partners\\partnerform.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\partners\\partnerform.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\partners\\partnerform.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\partners\\partnerform.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\partners\\partners.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\partners\\partners.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3FCB00B9-3AC3-4E3C-A0C4-D63B67213968}|..\\..\\Libraries\\Core.Themis.Libraries.DTO\\Core.Themis.Libraries.DTO.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\libraries\\core.themis.libraries.dto\\structuredto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5A450D3A-4E7F-431C-8DAA-97F1D02D79C0}|..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Core.Themis.Libraries.Data.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\libraries\\core.themis.libraries.data\\core.themis.librairies.data\\libraries_sqlscripts\\partner\\getstructureswithpartenaires.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}"}, {"AbsoluteMoniker": "D:0:0:{5A450D3A-4E7F-431C-8DAA-97F1D02D79C0}|..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Core.Themis.Libraries.Data.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\libraries\\core.themis.libraries.data\\core.themis.librairies.data\\repositories\\common\\genericrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\preparationmisevente\\preparationmisevente.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\preparationmisevente\\preparationmisevente.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\preparationmisevente\\preparationmisevente.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\preparationmisevente\\preparationmisevente.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\resources\\resource.de.resx||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\resources\\resource.de.resx||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\resources\\resource.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\resources\\resource.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\gestionmaquetteabofermer\\gestionmaquetteabofermer.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\gestionmaquetteabofermer\\gestionmaquetteabofermer.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\gestionmaquetteabofermer\\gestionmaquetteabofermer.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\gestionmaquetteabofermer\\gestionmaquetteabofermer.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\gestionroles\\createnewgroupmodal.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\gestionroles\\createnewgroupmodal.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\gestionroles\\createnewgroupmodal.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\gestionroles\\createnewgroupmodal.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\gestionroles\\rolemanagement.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\gestionroles\\rolemanagement.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\gestionroles\\createnewrolemodal.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\gestionroles\\createnewrolemodal.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\gestionroles\\createnewrolemodal.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\gestionroles\\createnewrolemodal.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\gestionroles\\createnewmodulemodal.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\gestionroles\\createnewmodulemodal.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\gestionroles\\createnewmodulemodal.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\gestionroles\\createnewmodulemodal.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\app.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\app.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\layout\\mainlayout.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\layout\\mainlayout.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\layout\\navmenu.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\layout\\navmenu.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\scripts\\insertlistestructurewithpartenairesmodule.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\scripts\\insertlistestructurewithpartenairesmodule.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\configfiles\\configinikeysofsection.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\configfiles\\configinikeysofsection.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\gestiontemppanier\\modifiertemppanier.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\gestiontemppanier\\modifiertemppanier.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\gestiontemppanier\\modifiertemppanier.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\gestiontemppanier\\modifiertemppanier.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5A450D3A-4E7F-431C-8DAA-97F1D02D79C0}|..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Core.Themis.Libraries.Data.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\libraries\\core.themis.libraries.data\\core.themis.librairies.data\\repositories\\queuing\\queuingrepository .cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{629BDF81-E618-42FF-A7ED-1217D2B26C14}|..\\..\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\libraries\\core.themis.libraries.razor\\core.themis.libraries.razor\\common\\components\\select\\select2.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{629BDF81-E618-42FF-A7ED-1217D2B26C14}|..\\..\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\libraries\\core.themis.libraries.razor\\core.themis.libraries.razor\\common\\components\\select\\newselect2.razor.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{629BDF81-E618-42FF-A7ED-1217D2B26C14}|..\\..\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\libraries\\core.themis.libraries.razor\\core.themis.libraries.razor\\common\\components\\select\\select2.razor.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\wwwroot\\js\\script.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\wwwroot\\js\\script.js||{14D17961-FE51-464D-9111-C4AF11D7D99A}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\configfiles\\configini.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\configfiles\\configini.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\home.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\home.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{95E6348E-C5AA-4836-BAC6-EF0845F452F8}|..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\libraries\\core.themis.libraries.bll\\core.themis.libraries.bll\\abonnementmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{95E6348E-C5AA-4836-BAC6-EF0845F452F8}|..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\libraries\\core.themis.libraries.bll\\core.themis.libraries.bll\\interfaces\\iabonnementmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\couponspromo\\couponspromo.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\couponspromo\\couponspromo.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\transferepointagephoto\\transferepointagephoto.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\transferepointagephoto\\transferepointagephoto.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\couponspromo\\couponspromo.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\couponspromo\\couponspromo.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{5A450D3A-4E7F-431C-8DAA-97F1D02D79C0}|..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Core.Themis.Libraries.Data.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\libraries\\core.themis.libraries.data\\core.themis.librairies.data\\repositories\\queuinghistopassages\\interfaces\\iqueuinghistopassagesrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5A450D3A-4E7F-431C-8DAA-97F1D02D79C0}|..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Core.Themis.Libraries.Data.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\libraries\\core.themis.libraries.data\\core.themis.librairies.data\\repositories\\queuinghistopassages\\queuinghistopassagesrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\configfiles\\configini.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\configfiles\\configini.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\configfiles\\configinisectionswitch.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\configfiles\\configinisectionswitch.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5A450D3A-4E7F-431C-8DAA-97F1D02D79C0}|..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Core.Themis.Libraries.Data.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\libraries\\core.themis.libraries.data\\core.themis.librairies.data\\repositories\\couponspromo\\couponspromorepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{95E6348E-C5AA-4836-BAC6-EF0845F452F8}|..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\libraries\\core.themis.libraries.bll\\core.themis.libraries.bll\\queuing\\queuingmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\configfiles\\configinikeysofsection.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\configfiles\\configinikeysofsection.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\configfiles\\configinisectionaccordion.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\configfiles\\configinisectionaccordion.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\configfiles\\configinisectionswitch.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\configfiles\\configinisectionswitch.razor||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\intranet\\themissupporttools.web\\themissupporttools.web\\components\\pages\\modules\\configfiles\\configinisectionaccordion.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DCD2C40A-9CF6-4B62-9FEE-7C82650B4B68}|ThemisSupportTools.Web\\ThemisSupportTools.Web.csproj|solutionrelative:themissupporttools.web\\components\\pages\\modules\\configfiles\\configinisectionaccordion.razor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{95E6348E-C5AA-4836-BAC6-EF0845F452F8}|..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\libraries\\core.themis.libraries.bll\\core.themis.libraries.bll\\managers\\tst\\themissupporttoolsmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5A450D3A-4E7F-431C-8DAA-97F1D02D79C0}|..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Core.Themis.Libraries.Data.csproj|d:\\work\\tst_ld\\themissupporttools_ld\\libraries\\core.themis.libraries.data\\core.themis.librairies.data\\libraries_sqlscripts\\partner\\getstructureswithpartenaires.sql||{CC5D8DF0-88F4-4BB2-9DBB-B48CEE65C30A}|CodeFrame"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 190, "SelectedChildIndex": 16, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{65ddf8c3-8f89-4077-a6c6-dbb8853aab13}"}, {"$type": "Bookmark", "Name": "ST:11:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:12:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:0:0:{e1b7d1f8-9b3c-49b1-8f4f-bfc63a88835d}"}, {"$type": "Bookmark", "Name": "ST:0:0:{e5c86464-96be-4d7c-9a8b-abcb3bbf5f92}"}, {"$type": "Document", "DocumentIndex": 4, "Title": "RevendeurRepository.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\WSAdmin\\RevendeurRepository.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\WSAdmin\\RevendeurRepository.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\WSAdmin\\RevendeurRepository.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\WSAdmin\\RevendeurRepository.cs", "ViewState": "AgIAABkAAAAAAAAAAAAgwCEAAAAPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T08:51:21.122Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "RevendeurDTO.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.DTO\\RevendeurDTO.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.DTO\\RevendeurDTO.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.DTO\\RevendeurDTO.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.DTO\\RevendeurDTO.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T08:50:56.669Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "BuyerProfilDTO.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.DTO\\BuyerProfilDTO.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.DTO\\BuyerProfilDTO.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.DTO\\BuyerProfilDTO.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.DTO\\BuyerProfilDTO.cs", "ViewState": "AgIAABcAAAAAAAAAAAA8wAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T08:50:46.636Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "IPartnerManager.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Interfaces\\IPartnerManager.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Interfaces\\IPartnerManager.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Interfaces\\IPartnerManager.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Interfaces\\IPartnerManager.cs", "ViewState": "AgIAADIAAAAAAAAAAAAmwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T15:33:33.391Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "BuyerProfilRepository.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\Open\\BuyerProfil\\BuyerProfilRepository.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\Open\\BuyerProfil\\BuyerProfilRepository.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\Open\\BuyerProfil\\BuyerProfilRepository.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\Open\\BuyerProfil\\BuyerProfilRepository.cs", "ViewState": "AgIAAKgAAAAAAAAAAAAqwLsAAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T14:44:16.425Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "RodrigueServicesExtension.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Extentions\\ServicesBuilder\\RodrigueServicesExtension.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Extentions\\ServicesBuilder\\RodrigueServicesExtension.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Extentions\\ServicesBuilder\\RodrigueServicesExtension.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Extentions\\ServicesBuilder\\RodrigueServicesExtension.cs", "ViewState": "AgIAAIUBAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T14:26:44.308Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "RevendeurIsProfilAcheteurOnStructureDTO.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.DTO\\RevendeurIsProfilAcheteurOnStructureDTO.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.DTO\\RevendeurIsProfilAcheteurOnStructureDTO.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.DTO\\RevendeurIsProfilAcheteurOnStructureDTO.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.DTO\\RevendeurIsProfilAcheteurOnStructureDTO.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T14:19:44.132Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "RevendeurIsProfilAcheteurOnStructureRepository.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\Open\\Partner\\RevendeurIsProfilAcheteurOnStructureRepository.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\Open\\Partner\\RevendeurIsProfilAcheteurOnStructureRepository.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\Open\\Partner\\RevendeurIsProfilAcheteurOnStructureRepository.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\Open\\Partner\\RevendeurIsProfilAcheteurOnStructureRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAACWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T14:18:58.949Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "IRevendeurIsProfilAcheteurOnStructureRepository.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\Open\\Partner\\Interfaces\\IRevendeurIsProfilAcheteurOnStructureRepository.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\Open\\Partner\\Interfaces\\IRevendeurIsProfilAcheteurOnStructureRepository.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\Open\\Partner\\Interfaces\\IRevendeurIsProfilAcheteurOnStructureRepository.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\Open\\Partner\\Interfaces\\IRevendeurIsProfilAcheteurOnStructureRepository.cs", "ViewState": "AgIAABwAAAAAAAAAAAAgwCsAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T14:18:48.415Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "RevendeurIsProfilAcheteurOnStructureEntity.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Entities\\Open\\Partner\\RevendeurIsProfilAcheteurOnStructureEntity.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Entities\\Open\\Partner\\RevendeurIsProfilAcheteurOnStructureEntity.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Entities\\Open\\Partner\\RevendeurIsProfilAcheteurOnStructureEntity.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Entities\\Open\\Partner\\RevendeurIsProfilAcheteurOnStructureEntity.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABoAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T14:18:34.062Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "ThemisSupportToolsProfiles.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\AutoMapperProfiles\\ThemisSupportToolsProfiles.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\AutoMapperProfiles\\ThemisSupportToolsProfiles.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\AutoMapperProfiles\\ThemisSupportToolsProfiles.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\AutoMapperProfiles\\ThemisSupportToolsProfiles.cs", "ViewState": "AgIAAIoAAAAAAAAAAAAQwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T14:16:43.967Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "LiaisonAcheteurrRevendeur.razor.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\LiaisonAcheteurrRevendeur\\LiaisonAcheteurrRevendeur.razor.cs", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\LiaisonAcheteurrRevendeur\\LiaisonAcheteurrRevendeur.razor.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\LiaisonAcheteurrRevendeur\\LiaisonAcheteurrRevendeur.razor.cs", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\LiaisonAcheteurrRevendeur\\LiaisonAcheteurrRevendeur.razor.cs", "ViewState": "AgIAAFMAAAAAAAAAAAAvwFQAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T13:09:50.221Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "LiaisonAcheteurrRevendeur.razor", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\LiaisonAcheteurrRevendeur\\LiaisonAcheteurrRevendeur.razor", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\LiaisonAcheteurrRevendeur\\LiaisonAcheteurrRevendeur.razor", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\LiaisonAcheteurrRevendeur\\LiaisonAcheteurrRevendeur.razor", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\LiaisonAcheteurrRevendeur\\LiaisonAcheteurrRevendeur.razor", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-09T13:09:29.853Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 24, "Title": "StructureDTO.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.DTO\\StructureDTO.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.DTO\\StructureDTO.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.DTO\\StructureDTO.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.DTO\\StructureDTO.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-09T11:32:39.367Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 23, "Title": "Partners.razor.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\Partners\\Partners.razor.cs", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\Partners\\Partners.razor.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\Partners\\Partners.razor.cs", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\Partners\\Partners.razor.cs", "ViewState": "AgIAAAABAAAAAAAAAAAwwBQBAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T15:04:46.992Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 27, "Title": "PreparationMiseVente.razor.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\PreparationMiseVente\\PreparationMiseVente.razor.cs", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\PreparationMiseVente\\PreparationMiseVente.razor.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\PreparationMiseVente\\PreparationMiseVente.razor.cs", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\PreparationMiseVente\\PreparationMiseVente.razor.cs", "ViewState": "AgIAAKAAAAAAAAAAAAAywCMAAABIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T14:29:52.314Z"}, {"$type": "Document", "DocumentIndex": 28, "Title": "PreparationMiseVente.razor", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\PreparationMiseVente\\PreparationMiseVente.razor", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\PreparationMiseVente\\PreparationMiseVente.razor", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\PreparationMiseVente\\PreparationMiseVente.razor", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\PreparationMiseVente\\PreparationMiseVente.razor", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-08T14:29:50.59Z"}, {"$type": "Document", "DocumentIndex": 29, "Title": "Resource.de.resx", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Resources\\Resource.de.resx", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Resources\\Resource.de.resx", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Resources\\Resource.de.resx", "RelativeToolTip": "ThemisSupportTools.Web\\Resources\\Resource.de.resx", "ViewState": "AgIAAAQJAAAAAAAAAAAuwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001005|", "WhenOpened": "2025-07-08T13:34:34.77Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 30, "Title": "Resource.Designer.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Resources\\Resource.Designer.cs", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Resources\\Resource.Designer.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Resources\\Resource.Designer.cs", "RelativeToolTip": "ThemisSupportTools.Web\\Resources\\Resource.Designer.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T13:32:36.887Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 31, "Title": "GestionMaquetteAboFermer.razor.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionMaquetteAboFermer\\GestionMaquetteAboFermer.razor.cs", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionMaquetteAboFermer\\GestionMaquetteAboFermer.razor.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionMaquetteAboFermer\\GestionMaquetteAboFermer.razor.cs", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionMaquetteAboFermer\\GestionMaquetteAboFermer.razor.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T13:26:51.984Z"}, {"$type": "Document", "DocumentIndex": 32, "Title": "GestionMaquetteAboFermer.razor", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionMaquetteAboFermer\\GestionMaquetteAboFermer.razor", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionMaquetteAboFermer\\GestionMaquetteAboFermer.razor", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionMaquetteAboFermer\\GestionMaquetteAboFermer.razor", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionMaquetteAboFermer\\GestionMaquetteAboFermer.razor", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-08T13:26:50.285Z"}, {"$type": "Document", "DocumentIndex": 35, "Title": "RoleManagement.razor", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionRoles\\RoleManagement.razor", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionRoles\\RoleManagement.razor", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionRoles\\RoleManagement.razor", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionRoles\\RoleManagement.razor", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-08T13:26:33.91Z"}, {"$type": "Document", "DocumentIndex": 36, "Title": "CreateNewRoleModal.razor.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionRoles\\CreateNewRoleModal.razor.cs", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionRoles\\CreateNewRoleModal.razor.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionRoles\\CreateNewRoleModal.razor.cs", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionRoles\\CreateNewRoleModal.razor.cs", "ViewState": "AgIAABMAAAAAAAAAAAAcwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T13:26:26.53Z"}, {"$type": "Document", "DocumentIndex": 37, "Title": "CreateNewRoleModal.razor", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionRoles\\CreateNewRoleModal.razor", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionRoles\\CreateNewRoleModal.razor", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionRoles\\CreateNewRoleModal.razor", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionRoles\\CreateNewRoleModal.razor", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-08T13:26:24.95Z"}, {"$type": "Document", "DocumentIndex": 38, "Title": "CreateNewModuleModal.razor.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionRoles\\CreateNewModuleModal.razor.cs", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionRoles\\CreateNewModuleModal.razor.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionRoles\\CreateNewModuleModal.razor.cs", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionRoles\\CreateNewModuleModal.razor.cs", "ViewState": "AgIAAAcAAAAAAAAAAAA1wAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T13:26:18.666Z"}, {"$type": "Document", "DocumentIndex": 33, "Title": "CreateNewGroupModal.razor.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionRoles\\CreateNewGroupModal.razor.cs", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionRoles\\CreateNewGroupModal.razor.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionRoles\\CreateNewGroupModal.razor.cs", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionRoles\\CreateNewGroupModal.razor.cs", "ViewState": "AgIAAAoAAAAAAAAAAAAgwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T13:26:04.991Z"}, {"$type": "Document", "DocumentIndex": 34, "Title": "CreateNewGroupModal.razor", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionRoles\\CreateNewGroupModal.razor", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionRoles\\CreateNewGroupModal.razor", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionRoles\\CreateNewGroupModal.razor", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionRoles\\CreateNewGroupModal.razor", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-08T13:26:03.375Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "app.css", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\wwwroot\\app.css", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\wwwroot\\app.css", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\wwwroot\\app.css", "RelativeToolTip": "ThemisSupportTools.Web\\wwwroot\\app.css", "ViewState": "AgIAABYCAAAAAAAAAAAAAJ4AAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003000|", "WhenOpened": "2025-07-08T13:16:07.117Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 41, "Title": "MainLayout.razor", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Layout\\MainLayout.razor", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Layout\\MainLayout.razor", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Layout\\MainLayout.razor", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Layout\\MainLayout.razor", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-08T13:12:36.596Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "Partners.razor", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\Partners\\Partners.razor", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\Partners\\Partners.razor", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\Partners\\Partners.razor", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\Partners\\Partners.razor", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-08T12:15:59.606Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 25, "Title": "getStructuresWithPartenaires.sql ", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\libraries_SQLScripts\\Partner\\getStructuresWithPartenaires.sql", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\libraries_SQLScripts\\Partner\\getStructuresWithPartenaires.sql", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\libraries_SQLScripts\\Partner\\getStructuresWithPartenaires.sql", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\libraries_SQLScripts\\Partner\\getStructuresWithPartenaires.sql", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000826|", "WhenOpened": "2025-07-08T11:57:11.879Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "TstAccessService.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Services\\Access\\TstAccessService.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Services\\Access\\TstAccessService.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Services\\Access\\TstAccessService.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Services\\Access\\TstAccessService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACgAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T10:24:30.873Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 43, "Title": "InsertListeStructureWithPartenairesModule.sql", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Scripts\\InsertListeStructureWithPartenairesModule.sql", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Scripts\\InsertListeStructureWithPartenairesModule.sql", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Scripts\\InsertListeStructureWithPartenairesModule.sql", "RelativeToolTip": "ThemisSupportTools.Web\\Scripts\\InsertListeStructureWithPartenairesModule.sql", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000826|", "WhenOpened": "2025-07-08T09:40:40.906Z"}, {"$type": "Document", "DocumentIndex": 42, "Title": "NavMenu.razor", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Layout\\NavMenu.razor", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Layout\\NavMenu.razor", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Layout\\NavMenu.razor", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Layout\\NavMenu.razor", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-08T09:26:16.644Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 18, "Title": "IPartnerRepository.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\WSAdmin\\Interfaces\\IPartnerRepository.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\WSAdmin\\Interfaces\\IPartnerRepository.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\WSAdmin\\Interfaces\\IPartnerRepository.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\WSAdmin\\Interfaces\\IPartnerRepository.cs", "ViewState": "AgIAAEYAAAAAAAAAAAAawFgAAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T09:04:20.932Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 17, "Title": "PartnerRepository.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\WSAdmin\\PartnerRepository.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\WSAdmin\\PartnerRepository.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\WSAdmin\\PartnerRepository.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\WSAdmin\\PartnerRepository.cs", "ViewState": "AgIAAKkBAAAAAAAAAAAkwL4BAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T08:14:25.532Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "PartnerManager.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\PartnerManager.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\PartnerManager.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\PartnerManager.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\PartnerManager.cs", "ViewState": "AgIAAJ0GAAAAAAAAAAAnwKQGAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T08:13:24.28Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 21, "Title": "PartnerForm.razor.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\Partners\\PartnerForm.razor.cs", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\Partners\\PartnerForm.razor.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\Partners\\PartnerForm.razor.cs", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\Partners\\PartnerForm.razor.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACMAAABSAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T08:12:28.116Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 22, "Title": "PartnerForm.razor", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\Partners\\PartnerForm.razor", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\Partners\\PartnerForm.razor", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\Partners\\PartnerForm.razor", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\Partners\\PartnerForm.razor", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-08T08:12:26.142Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 46, "Title": "ModifierTempPanier.razor.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionTempPanier\\ModifierTempPanier.razor.cs", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionTempPanier\\ModifierTempPanier.razor.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionTempPanier\\ModifierTempPanier.razor.cs", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionTempPanier\\ModifierTempPanier.razor.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-08T08:02:17.002Z"}, {"$type": "Document", "DocumentIndex": 45, "Title": "ModifierTempPanier.razor", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionTempPanier\\ModifierTempPanier.razor", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionTempPanier\\ModifierTempPanier.razor", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionTempPanier\\ModifierTempPanier.razor", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionTempPanier\\ModifierTempPanier.razor", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-08T07:58:06.761Z"}, {"$type": "Document", "DocumentIndex": 49, "Title": "NewSelect2.razor.js", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Select\\NewSelect2.razor.js", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Select\\NewSelect2.razor.js", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Select\\NewSelect2.razor.js", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Select\\NewSelect2.razor.js", "ViewState": "AgIAAA0AAAAAAAAAAAAiwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-07-04T14:06:17.963Z"}, {"$type": "Document", "DocumentIndex": 48, "Title": "Select2.razor", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Select\\Select2.razor", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Select\\Select2.razor", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Select\\Select2.razor", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Select\\Select2.razor", "ViewState": "AgIAAGkAAAAAAAAAAAAAAHMAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-04T14:06:11.738Z"}, {"$type": "Document", "DocumentIndex": 50, "Title": "Select2.razor.js", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Select\\Select2.razor.js", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Select\\Select2.razor.js", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Select\\Select2.razor.js", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\Common\\Components\\Select\\Select2.razor.js", "ViewState": "AgIAADkAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-07-04T14:06:08.649Z"}, {"$type": "Document", "DocumentIndex": 51, "Title": "script.js", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\wwwroot\\js\\script.js", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\wwwroot\\js\\script.js", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\wwwroot\\js\\script.js", "RelativeToolTip": "ThemisSupportTools.Web\\wwwroot\\js\\script.js", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001646|", "WhenOpened": "2025-07-04T14:05:51.655Z"}, {"$type": "Document", "DocumentIndex": 52, "Title": "ConfigIni.razor.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIni.razor.cs", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIni.razor.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIni.razor.cs", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIni.razor.cs", "ViewState": "AgIAADoCAAAAAAAAAAAUwFgCAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-04T13:40:52.264Z"}, {"$type": "Document", "DocumentIndex": 40, "Title": "App.razor", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\App.razor", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\App.razor", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\App.razor", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\App.razor", "ViewState": "AgIAAAYAAAAAAAAAAAAAABcAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-04T13:18:58.651Z"}, {"$type": "Document", "DocumentIndex": 53, "Title": "Home.razor", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Home.razor", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Home.razor", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Home.razor", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Home.razor", "ViewState": "AgIAABsAAAAAAAAAAAAAAPoAAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-04T09:01:26.227Z"}, {"$type": "Document", "DocumentIndex": 55, "Title": "IAbonnementManager.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Interfaces\\IAbonnementManager.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Interfaces\\IAbonnementManager.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Interfaces\\IAbonnementManager.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Interfaces\\IAbonnementManager.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-04T08:22:08.883Z"}, {"$type": "Document", "DocumentIndex": 56, "Title": "CouponsPromo.razor.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\CouponsPromo\\CouponsPromo.razor.cs", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\CouponsPromo\\CouponsPromo.razor.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\CouponsPromo\\CouponsPromo.razor.cs", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\CouponsPromo\\CouponsPromo.razor.cs", "ViewState": "AgIAAKMBAAAAAAAAAAAiwL0BAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T09:36:03.627Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "LieuPhysiqueRepository.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\LieuPhysique\\LieuPhysiqueRepository.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\LieuPhysique\\LieuPhysiqueRepository.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\LieuPhysique\\LieuPhysiqueRepository.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\LieuPhysique\\LieuPhysiqueRepository.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAAACIAAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T08:26:17.109Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 26, "Title": "GenericRepository.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\Common\\GenericRepository.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\Common\\GenericRepository.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\Common\\GenericRepository.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\Common\\GenericRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAEAAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T08:24:15.082Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 54, "Title": "AbonnementManager.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\AbonnementManager.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\AbonnementManager.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\AbonnementManager.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\AbonnementManager.cs", "ViewState": "AgIAABoAAAAAAAAAAAAxwCUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T07:07:36.071Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 59, "Title": "IQueuingHistopassagesRepository.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\QueuingHistopassages\\Interfaces\\IQueuingHistopassagesRepository.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\QueuingHistopassages\\Interfaces\\IQueuingHistopassagesRepository.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\QueuingHistopassages\\Interfaces\\IQueuingHistopassagesRepository.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\QueuingHistopassages\\Interfaces\\IQueuingHistopassagesRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAgAAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T07:46:23.507Z"}, {"$type": "Document", "DocumentIndex": 63, "Title": "CouponsPromoRepository.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\CouponsPromo\\CouponsPromoRepository.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\CouponsPromo\\CouponsPromoRepository.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\CouponsPromo\\CouponsPromoRepository.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\CouponsPromo\\CouponsPromoRepository.cs", "ViewState": "AgIAACUAAAAAAAAAAAAgwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T08:24:22.066Z"}, {"$type": "Document", "DocumentIndex": 58, "Title": "CouponsPromo.razor", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\CouponsPromo\\CouponsPromo.razor", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\CouponsPromo\\CouponsPromo.razor", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\CouponsPromo\\CouponsPromo.razor", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\CouponsPromo\\CouponsPromo.razor", "ViewState": "AgIAAFcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-03T09:35:58.146Z"}, {"$type": "Document", "DocumentIndex": 60, "Title": "QueuingHistopassagesRepository.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\QueuingHistopassages\\QueuingHistopassagesRepository.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\QueuingHistopassages\\QueuingHistopassagesRepository.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\QueuingHistopassages\\QueuingHistopassagesRepository.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\QueuingHistopassages\\QueuingHistopassagesRepository.cs", "ViewState": "AgIAADUAAAAAAAAAAAAQwEIAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T07:43:10.435Z"}, {"$type": "Document", "DocumentIndex": 57, "Title": "TransferePointagePhoto.razor.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\TransferePointagePhoto\\TransferePointagePhoto.razor.cs", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\TransferePointagePhoto\\TransferePointagePhoto.razor.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\TransferePointagePhoto\\TransferePointagePhoto.razor.cs", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\TransferePointagePhoto\\TransferePointagePhoto.razor.cs", "ViewState": "AgIAABUBAAAAAAAAAAAwwLgAAAA0AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T07:07:36.055Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 19, "Title": "RoleManagement.razor.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionRoles\\RoleManagement.razor.cs", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionRoles\\RoleManagement.razor.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionRoles\\RoleManagement.razor.cs", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionRoles\\RoleManagement.razor.cs", "ViewState": "AgIAALwAAAAAAAAAAAAiwMcAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T07:07:36.055Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 64, "Title": "QueuingManager.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Queuing\\QueuingManager.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Queuing\\QueuingManager.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Queuing\\QueuingManager.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Queuing\\QueuingManager.cs", "ViewState": "AgIAABwAAAAAAAAAAAAgwCsAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T07:07:36.071Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 47, "Title": "QueuingRepository .cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\Queuing\\QueuingRepository .cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\Queuing\\QueuingRepository .cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\Queuing\\QueuingRepository .cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.Data\\Core.Themis.Librairies.Data\\Repositories\\Queuing\\QueuingRepository .cs", "ViewState": "AgIAAFUAAAAAAAAAAAAowGIAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T07:07:36.087Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 39, "Title": "CreateNewModuleModal.razor", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionRoles\\CreateNewModuleModal.razor", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionRoles\\CreateNewModuleModal.razor", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionRoles\\CreateNewModuleModal.razor", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\GestionRoles\\CreateNewModuleModal.razor", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-06-25T08:24:04.17Z"}, {"$type": "Document", "DocumentIndex": 62, "Title": "ConfigIniSectionSwitch.razor.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIniSectionSwitch.razor.cs", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIniSectionSwitch.razor.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIniSectionSwitch.razor.cs", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIniSectionSwitch.razor.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAA3AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T07:38:52.967Z"}, {"$type": "Document", "DocumentIndex": 67, "Title": "ConfigIniSectionSwitch.razor", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIniSectionSwitch.razor", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIniSectionSwitch.razor", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIniSectionSwitch.razor", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIniSectionSwitch.razor", "ViewState": "AgIAAAAAAAAAAAAAAAAAABYAAAB5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-06-25T07:38:51.357Z"}, {"$type": "Document", "DocumentIndex": 65, "Title": "ConfigIniKeysOfSection.razor.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIniKeysOfSection.razor.cs", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIniKeysOfSection.razor.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIniKeysOfSection.razor.cs", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIniKeysOfSection.razor.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAswB4AAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T07:38:18.029Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 66, "Title": "ConfigIniSectionAccordion.razor", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIniSectionAccordion.razor", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIniSectionAccordion.razor", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIniSectionAccordion.razor", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIniSectionAccordion.razor", "ViewState": "AgIAAC0AAAAAAAAAAAAAAEAAAABVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-06-25T07:38:31.677Z"}, {"$type": "Document", "DocumentIndex": 44, "Title": "ConfigIniKeysOfSection.razor", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIniKeysOfSection.razor", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIniKeysOfSection.razor", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIniKeysOfSection.razor", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIniKeysOfSection.razor", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-06-25T07:38:15.979Z"}, {"$type": "Document", "DocumentIndex": 68, "Title": "ConfigIniSectionAccordion.razor.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIniSectionAccordion.razor.cs", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIniSectionAccordion.razor.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIniSectionAccordion.razor.cs", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIniSectionAccordion.razor.cs", "ViewState": "AgIAAKcAAAAAAAAAAAAqwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T07:38:32.921Z"}, {"$type": "Document", "DocumentIndex": 61, "Title": "ConfigIni.razor", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIni.razor", "RelativeDocumentMoniker": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIni.razor", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Intranet\\ThemisSupportTools.Web\\ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIni.razor", "RelativeToolTip": "ThemisSupportTools.Web\\Components\\Pages\\Modules\\ConfigFiles\\ConfigIni.razor", "ViewState": "AgIAAFoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-06-25T07:33:25.452Z"}, {"$type": "Document", "DocumentIndex": 69, "Title": "ThemisSupportToolsManager.cs", "DocumentMoniker": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Managers\\TST\\ThemisSupportToolsManager.cs", "RelativeDocumentMoniker": "..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Managers\\TST\\ThemisSupportToolsManager.cs", "ToolTip": "D:\\WORK\\TST_LD\\ThemisSupportTools_LD\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Managers\\TST\\ThemisSupportToolsManager.cs", "RelativeToolTip": "..\\..\\Libraries\\Core.Themis.Libraries.BLL\\Core.Themis.Libraries.BLL\\Managers\\TST\\ThemisSupportToolsManager.cs", "ViewState": "AgIAAAkFAAAAAAAAAAApwGkFAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T07:34:31.426Z"}]}]}]}