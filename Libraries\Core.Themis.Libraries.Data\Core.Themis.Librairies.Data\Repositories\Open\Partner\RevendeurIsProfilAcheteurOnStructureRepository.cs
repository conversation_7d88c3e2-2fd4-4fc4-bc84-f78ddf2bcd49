using AutoMapper;
using Core.Themis.Libraries.Data.DBContext;
using Core.Themis.Libraries.Data.DBContext.Interfaces;
using Core.Themis.Libraries.Data.Entities.Open.Partner;
using Core.Themis.Libraries.Data.Repositories.Common;
using Core.Themis.Libraries.Data.Repositories.Open.Partner.Interfaces;
using Dapper;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Threading.Tasks;

namespace Core.Themis.Libraries.Data.Repositories.Open.Partner
{
    public class RevendeurIsProfilAcheteurOnStructureRepository : GenericStructureRepository<RevendeurIsProfilAcheteurOnStructureEntity>, IRevendeurIsProfilAcheteurOnStructureRepository
    {
        private readonly IDbContext _dbContext;
        private readonly IMapper _mapper;

        public RevendeurIsProfilAcheteurOnStructureRepository(
            IDbContext dbContext,
            IMapper mapper,
            IMemoryCache memoryCache,
            IConfiguration config) : base(ContextType.Open, dbContext, memoryCache, mapper)
        {
            _dbContext = dbContext;
            _mapper = mapper;
        }

        public async Task<IEnumerable<RevendeurIsProfilAcheteurOnStructureEntity>> GetLiaisonsByRevendeurAsync(int structureId, int revendeurId)
        {
            using SqlConnection connection = _dbContext.GetStructureConnection($"{structureId:0000}");
            connection.Open();

            string sqlQuery = @"
                SELECT * FROM revendeur_is_profil_acheteur_on_structure 
                WHERE revendeur_id = @RevendeurId";

            return await connection.QueryAsync<RevendeurIsProfilAcheteurOnStructureEntity>(sqlQuery, new { RevendeurId = revendeurId });
        }

        public async Task<IEnumerable<RevendeurIsProfilAcheteurOnStructureEntity>> GetLiaisonsByStructureAsync(int structureId)
        {
            using SqlConnection connection = _dbContext.GetStructureConnection($"{structureId:0000}");
            connection.Open();

            string sqlQuery = @"
                SELECT * FROM revendeur_is_profil_acheteur_on_structure 
                WHERE structure_id = @StructureId";

            return await connection.QueryAsync<RevendeurIsProfilAcheteurOnStructureEntity>(sqlQuery, new { StructureId = structureId });
        }

        public async Task<IEnumerable<RevendeurIsProfilAcheteurOnStructureEntity>> GetLiaisonsByProfilAcheteurAsync(int structureId, int profilAcheteurId)
        {
            using SqlConnection connection = _dbContext.GetStructureConnection($"{structureId:0000}");
            connection.Open();

            string sqlQuery = @"
                SELECT * FROM revendeur_is_profil_acheteur_on_structure 
                WHERE profil_acheteur_id = @ProfilAcheteurId";

            return await connection.QueryAsync<RevendeurIsProfilAcheteurOnStructureEntity>(sqlQuery, new { ProfilAcheteurId = profilAcheteurId });
        }

        public async Task<bool> LiaisonExistsAsync(int structureId, int revendeurId, int profilAcheteurId)
        {
            using SqlConnection connection = _dbContext.GetStructureConnection($"{structureId:0000}");
            connection.Open();

            string sqlQuery = @"
                SELECT COUNT(1) FROM revendeur_is_profil_acheteur_on_structure 
                WHERE revendeur_id = @RevendeurId 
                AND structure_id = @StructureId 
                AND profil_acheteur_id = @ProfilAcheteurId";

            var count = await connection.QuerySingleAsync<int>(sqlQuery, new 
            { 
                RevendeurId = revendeurId, 
                StructureId = structureId, 
                ProfilAcheteurId = profilAcheteurId 
            });

            return count > 0;
        }

        public async Task<bool> DeleteLiaisonAsync(int structureId, int revendeurId, int profilAcheteurId)
        {
            using SqlConnection connection = _dbContext.GetStructureConnection($"{structureId:0000}");
            connection.Open();

            string sqlQuery = @"
                DELETE FROM revendeur_is_profil_acheteur_on_structure 
                WHERE revendeur_id = @RevendeurId 
                AND structure_id = @StructureId 
                AND profil_acheteur_id = @ProfilAcheteurId";

            var rowsAffected = await connection.ExecuteAsync(sqlQuery, new 
            { 
                RevendeurId = revendeurId, 
                StructureId = structureId, 
                ProfilAcheteurId = profilAcheteurId 
            });

            return rowsAffected > 0;
        }
    }
}
