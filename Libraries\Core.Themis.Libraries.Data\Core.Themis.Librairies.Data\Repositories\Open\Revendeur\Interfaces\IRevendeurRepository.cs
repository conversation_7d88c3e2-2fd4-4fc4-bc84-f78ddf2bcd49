using Core.Themis.Libraries.Data.Entities.Open.Revendeur;
using Core.Themis.Libraries.Data.Repositories.Common.Interfaces;

namespace Core.Themis.Libraries.Data.Repositories.Open.Revendeur.Interfaces
{
    public interface IRevendeurRepository : IGenericStructureRepository<RevendeurEntity>
    {
        /// <summary>
        /// Récupère tous les revendeurs de la table revendeurs
        /// </summary>
        /// <param name="structureId">ID de la structure pour la connexion</param>
        /// <returns>Liste des revendeurs</returns>
        IEnumerable<RevendeurEntity> GetAllRevendeurs(int structureId);
    }
}
