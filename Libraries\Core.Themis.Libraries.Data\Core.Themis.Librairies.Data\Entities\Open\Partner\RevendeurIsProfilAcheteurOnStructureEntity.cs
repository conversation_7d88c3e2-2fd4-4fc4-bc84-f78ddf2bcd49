using Core.Themis.Libraries.Data.Entities.Common;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Core.Themis.Libraries.Data.Entities.Open.Partner
{
    [Table("revendeur_is_profil_acheteur_on_structure")]
    public class RevendeurIsProfilAcheteurOnStructureEntity : GenericEntity
    {
        [Key]
        [Column("revendeur_id")]
        public int RevendeurId { get; set; }

        [Key]
        [Column("structure_id")]
        public int StructureId { get; set; }

        [Key]
        [Column("profil_acheteur_id")]
        public int ProfilAcheteurId { get; set; }

        [Column("date_operation")]
        public DateTime DateOperation { get; set; }

        [Column("operateur_id")]
        public int OperateurId { get; set; }
    }
}
