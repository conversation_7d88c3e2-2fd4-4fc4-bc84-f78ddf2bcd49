﻿using AutoMapper;
using Core.Themis.Libraries.BLL.AccessControl;
using Core.Themis.Libraries.BLL.AccessControl.Interfaces;
using Core.Themis.Libraries.BLL.adhesion_offres;
using Core.Themis.Libraries.BLL.adhesion_offres.Interfaces;
using Core.Themis.Libraries.BLL.AutoMapperProfiles;
using Core.Themis.Libraries.BLL.CarnetTickets;
using Core.Themis.Libraries.BLL.CarnetTickets.Interfaces;
using Core.Themis.Libraries.BLL.EventsSessions;
using Core.Themis.Libraries.BLL.EventsSessions.Interfaces;
using Core.Themis.Libraries.BLL.Extentions.ServicesBuilder.Interfaces;
using Core.Themis.Libraries.BLL.Helpers;
using Core.Themis.Libraries.BLL.HomeModular;
using Core.Themis.Libraries.BLL.InfoComp.Interfaces;
using Core.Themis.Libraries.BLL.Insurance;
using Core.Themis.Libraries.BLL.Insurance.Interfaces;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.BLL.Managers;
using Core.Themis.Libraries.BLL.Managers.Access;
using Core.Themis.Libraries.BLL.Managers.Access.Interfaces;
using Core.Themis.Libraries.BLL.Managers.Catalog;
using Core.Themis.Libraries.BLL.Managers.Catalog.Interfaces;
using Core.Themis.Libraries.BLL.Managers.Consumer;
using Core.Themis.Libraries.BLL.Managers.Consumer.Interfaces;
using Core.Themis.Libraries.BLL.Managers.DepotVente;
using Core.Themis.Libraries.BLL.Managers.DepotVente.Interfaces;
using Core.Themis.Libraries.BLL.Managers.Export;
using Core.Themis.Libraries.BLL.Managers.Export.Interfaces;
using Core.Themis.Libraries.BLL.Managers.HomeModular.Interfaces;
using Core.Themis.Libraries.BLL.Managers.Interfaces;
using Core.Themis.Libraries.BLL.Managers.Order;
using Core.Themis.Libraries.BLL.Managers.Order.Interfaces;
using Core.Themis.Libraries.BLL.Managers.Order.OpinionOrder;
using Core.Themis.Libraries.BLL.Managers.Order.OpinionOrder.Interfaces;
using Core.Themis.Libraries.BLL.Managers.Queue;
using Core.Themis.Libraries.BLL.Managers.Queue.Interfaces;
using Core.Themis.Libraries.BLL.Managers.Sales;
using Core.Themis.Libraries.BLL.Managers.Sales.Interfaces;
using Core.Themis.Libraries.BLL.Managers.Traduction;
using Core.Themis.Libraries.BLL.Managers.Traduction.Interfaces;
using Core.Themis.Libraries.BLL.Managers.TST;
using Core.Themis.Libraries.BLL.Managers.TST.Interfaces;
using Core.Themis.Libraries.BLL.OrderDetails;
using Core.Themis.Libraries.BLL.OrderDetails.Interfaces;
using Core.Themis.Libraries.BLL.Partners;
using Core.Themis.Libraries.BLL.Partners.Interfaces;
using Core.Themis.Libraries.BLL.Products.Interfaces;
using Core.Themis.Libraries.BLL.Services;
using Core.Themis.Libraries.BLL.Services.Interfaces;
using Core.Themis.Libraries.BLL.Services.PassCulture;
using Core.Themis.Libraries.BLL.Services.PassCulture.Interfaces;
using Core.Themis.Libraries.BLL.Transactionnals;
using Core.Themis.Libraries.BLL.Transactionnals.Interfaces;
using Core.Themis.Libraries.BLL.Translations;
using Core.Themis.Libraries.BLL.Translations.Interfaces;
using Core.Themis.Libraries.BLL.WsAdmin;
using Core.Themis.Libraries.BLL.WsAdmin.Interfaces;
using Core.Themis.Libraries.BLL.WT;
using Core.Themis.Libraries.BLL.WT.Interfaces;
using Core.Themis.Libraries.Data.DBContext;
using Core.Themis.Libraries.Data.DBContext.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Export;
using Core.Themis.Libraries.Data.Repositories.Export.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Abonnement;
using Core.Themis.Libraries.Data.Repositories.Open.Abonnement.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.AccessControl;
using Core.Themis.Libraries.Data.Repositories.Open.AccessControl.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Adhesion;
using Core.Themis.Libraries.Data.Repositories.Open.Adhesion.Interface;
using Core.Themis.Libraries.Data.Repositories.Open.BuyerProfil;
using Core.Themis.Libraries.Data.Repositories.Open.BuyerProfil.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.CarnetTickets;
using Core.Themis.Libraries.Data.Repositories.Open.CarnetTickets.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Cinema;
using Core.Themis.Libraries.Data.Repositories.Open.Cinema.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Consumer;
using Core.Themis.Libraries.Data.Repositories.Open.Consumer.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.DepotVente;
using Core.Themis.Libraries.Data.Repositories.Open.DepotVente.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.EventsSessions;
using Core.Themis.Libraries.Data.Repositories.Open.EventsSessions.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.GestionPlaces;
using Core.Themis.Libraries.Data.Repositories.Open.GestionPlaces.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Histo;
using Core.Themis.Libraries.Data.Repositories.Open.Histo.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.HomeModular;
using Core.Themis.Libraries.Data.Repositories.Open.HomeModular.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Identities;
using Core.Themis.Libraries.Data.Repositories.Open.Identities.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.InfoComp;
using Core.Themis.Libraries.Data.Repositories.Open.InfoComp.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Insurance;
using Core.Themis.Libraries.Data.Repositories.Open.Insurance.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Language;
using Core.Themis.Libraries.Data.Repositories.Open.Language.Intrefaces;
using Core.Themis.Libraries.Data.Repositories.Open.Lieu;
using Core.Themis.Libraries.Data.Repositories.Open.Lieu.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Maquette;
using Core.Themis.Libraries.Data.Repositories.Open.Maquette.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Orders;
using Core.Themis.Libraries.Data.Repositories.Open.Orders.Dossier;
using Core.Themis.Libraries.Data.Repositories.Open.Orders.Dossier.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Orders.Entree;
using Core.Themis.Libraries.Data.Repositories.Open.Orders.Entree.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Orders.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Orders.OrderDetails;
using Core.Themis.Libraries.Data.Repositories.Open.Orders.OrderDetails.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Orders.OrderOpinion;
using Core.Themis.Libraries.Data.Repositories.Open.Orders.OrderOpinion.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Partner;
using Core.Themis.Libraries.Data.Repositories.Open.Partner.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.PlaceObjects;
using Core.Themis.Libraries.Data.Repositories.Open.PlaceObjects.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Places;
using Core.Themis.Libraries.Data.Repositories.Open.Places.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Produit;
using Core.Themis.Libraries.Data.Repositories.Open.Produit.Boutique;
using Core.Themis.Libraries.Data.Repositories.Open.Produit.Boutique.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Produit.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Queue;
using Core.Themis.Libraries.Data.Repositories.Open.Queue.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Sales;
using Core.Themis.Libraries.Data.Repositories.Open.Sales.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Sponsors;
using Core.Themis.Libraries.Data.Repositories.Open.Sponsors.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Structure;
using Core.Themis.Libraries.Data.Repositories.Open.Structure.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Tarif;
using Core.Themis.Libraries.Data.Repositories.Open.Tarif.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Traduction;
using Core.Themis.Libraries.Data.Repositories.Open.Traduction.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Transactionnals;
using Core.Themis.Libraries.Data.Repositories.Open.Transactionnals.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Vues;
using Core.Themis.Libraries.Data.Repositories.Open.Vues.Interfaces;
using Core.Themis.Libraries.Data.Repositories.WebLibrary.Catalog;
using Core.Themis.Libraries.Data.Repositories.WebLibrary.Catalog.Interfaces;
using Core.Themis.Libraries.Data.Repositories.WebLibrary.Translate;
using Core.Themis.Libraries.Data.Repositories.WebLibrary.Translate.Interfaces;
using Core.Themis.Libraries.Data.Repositories.WebTracing;
using Core.Themis.Libraries.Data.Repositories.WebTracing.Interfaces;
using Core.Themis.Libraries.Data.Repositories.WebTracing.Panier;
using Core.Themis.Libraries.Data.Repositories.WebTracing.Panier.Interfaces;
using Core.Themis.Libraries.Data.Repositories.WSAdmin;
using Core.Themis.Libraries.Data.Repositories.WSAdmin.Interfaces;
using Core.Themis.Libraries.Data.Repositories.WSAdmin.ThemisSupportTools;
using Core.Themis.Libraries.Data.Repositories.WSAdmin.ThemisSupportTools.Interfaces;
using Core.Themis.Libraries.Data.StoredProcedure.Open;
using Core.Themis.Libraries.Data.StoredProcedure.Open.Interfaces;
using Core.Themis.Libraries.Utilities.Helpers;
using Core.Themis.Libraries.Utilities.Helpers.Cache;
using Core.Themis.Libraries.Utilities.Helpers.Cache.Interfaces;
using Core.Themis.Libraries.Utilities.Helpers.Interfaces;
using Core.Themis.Libraries.Utilities.Logging;
using Dapper;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Diagnostics;
using Core.Themis.Libraries.Data.Repositories.Open.ParamsDeflag.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.ParamsDeflag;
using Core.Themis.Libraries.Data.Repositories.CouponsPromo.Interfaces;
using Core.Themis.Libraries.Data.Repositories.CouponsPromo;
using Core.Themis.Libraries.Data.Repositories.LieuPhysique.Interface;
using Core.Themis.Libraries.Data.Repositories.LieuPhysique;
using Core.Themis.Libraries.Data.Repositories.Queuing;
using Core.Themis.Libraries.Data.Repositories.Queuing.Interfaces;
using Core.Themis.Libraries.Data.Repositories.QueuingHistopassages.Interfaces;
using Core.Themis.Libraries.BLL.Queuing;
using Core.Themis.Libraries.BLL.Queuing.Interfaces;
using Core.Themis.Libraries.Data.Repositories.QueuingHistopassages;
using Core.Themis.Libraries.BLL.Services.Login;
using Core.Themis.Libraries.Utilities.Helpers.Cryptography;
using StackExchange.Redis;
using Core.Themis.Libraries.BLL.Services.Pdf.Interfaces;
using Core.Themis.Libraries.BLL.Services.Pdf;
using Core.Themis.Libraries.Data.Repositories.Open.Jauges.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Jauges;
using Core.Themis.Libraries.BLL.Managers.Jauges.Interfaces;
using Core.Themis.Libraries.BLL.Managers.Jauges;



namespace Core.Themis.Libraries.BLL.Extentions.ServicesBuilder
{
    public static class RodrigueServicesExtension
    {
        /// <summary>
        /// Extention method for add rodrigue DbContext and Repositories in <see cref="IServiceCollection"/>
        /// </summary>
        /// <param name="services"></param>
        public static void AddRodrigueDataServices(this IServiceCollection services)
        {
            DefaultTypeMap.MatchNamesWithUnderscores = true;
            services.AddScoped<IDbContext, DbContext>();
            services.AddScoped<IRazorViewToStringRenderer, RazorViewToStringRenderer>();

            services.AddScoped<IThemisSupportToolsDbContext, ThemisSupportToolsDbContext>();
            services.AddScoped<IDbContextResolver, DbContextResolver>();

            services.AddScoped<IRepositoryFactory, RepositoryFactory>();

            services.AddScoped<IStructuresStoredProcedureAccessor, StructuresStoredProcedureAccessor>();
            services.AddScoped<IAccessControlRepository, AccessControlRepository>();
            services.AddScoped<IActionPaiementRepository,ActionPaiementRepository>();
            services.AddScoped<IBasketRepository, BasketRepository>();
            services.AddScoped<IGestionTraceRepository, GestionTraceRepository>();
            services.AddScoped<IBuyerProfilRepository, BuyerProfilRepository>();
            services.AddScoped<IRevendeurIsProfilAcheteurOnStructureRepository, RevendeurIsProfilAcheteurOnStructureRepository>();
            services.AddScoped<ICategoryRepository, CategoryRepository>();
            services.AddScoped<ICompteClientRepository, CompteClientRepository>();
            services.AddScoped<IDossierProduitAssuranceRepository, DossierProduitAssuranceRepository>();
            services.AddScoped<IDossierRepository, DossierRepository>();
            services.AddScoped<IEntreeRepository, EntreeRepository>();
            services.AddScoped<IEventRepository, EventRepository>();
            services.AddScoped<IFieldsCodeListRepository, FieldsCodeListRepository>();
            services.AddScoped<IFieldsGlobalTranslationRepository, FieldsGlobalTranslationRepository>();
            services.AddScoped<IGestionPlaceRepository, GestionPlaceRepository>();
            services.AddScoped<IIdentityRepository, IdentityRepository>();
            services.AddScoped<IGlobalAppellationRepository, GlobalAppellationRepository>();
            services.AddScoped<IInsuranceContractRepository, InsuranceContractRepository>();
            services.AddScoped<IInsuranceRepository, InsuranceRepository>();
            services.AddScoped<IIdentiteInfoCompRepository, IdentiteInfoCompRepository>();
            services.AddScoped<IOrderInfoRepository, OrderInfoRepository>();
            services.AddScoped<IOrderLineRepository, OrderLineRepository>();
            services.AddScoped<IOrderRepository, OrderRepository>();

            services.AddScoped<IEnvoiPdfdepuisOpenRepository, EnvoiPdfdepuisOpenRepository>();

            services.AddScoped<IPartnerRepository, PartnerRepository>();
            services.AddScoped<IRevendeurRepository, RevendeurRepository>();
            services.AddScoped<IPaymentMethodRepository, PaymentMethodRepository>();
            services.AddScoped<ISeatRepository, SeatRepository>();
            services.AddScoped<ISessionRepository, SessionRepository>();
            services.AddScoped<ISpecificClientTranslationRepository, SpecificClientTranslationRepository>();
            services.AddScoped<ISponsorPanierEntreesRepository, SponsorPanierEntreesRepository>();
            services.AddScoped<ISponsorRepository, SponsorRepository>();
            services.AddScoped<ISponsorPanierEntreesRepository, SponsorPanierEntreesRepository>();
            services.AddScoped<IStructureRepository, StructureRepository>();
            services.AddScoped<IStructuresRepository, StructuresRepository>();
            services.AddScoped<ITranslateAreasRepository, TranslateAreasRepository>();
            services.AddScoped<ITranslateVariablesRepository, TranslateVariablesRepository>();
            services.AddScoped<IValeurTarifStockRepository, ValeurTarifStockRepository>();
            services.AddScoped<IWebUserRepository, WebUserRepository>();
            services.AddScoped<IMaquetteRepository, MaquetteRepository>();
            services.AddScoped<IPlaceRepository, PlaceRepository>();
            services.AddScoped<IProduitRepository, ProduitRepository>();
            services.AddScoped<IRecetteRepository, RecetteRepository>();
            services.AddScoped<IStructurePrefsRepository, StructurePrefsRepository>();
            services.AddScoped<IProprietesOfManifsRepository, ProprietesOfManifsRepository>();
            services.AddScoped<IHomeModularRepository, HomeModularRepository>();
            services.AddScoped<IExportRepository, ExportRepository>();
            services.AddScoped<IEventGroupRepository, EventGroupRepository>();
            services.AddScoped<IEventGenreRepository, EventGenreRepository>();
            services.AddScoped<IEventSousGenreRepository, EventSousGenreRepository>();
            services.AddScoped<IBoutiqueRepository, BoutiqueRepository>();
            services.AddScoped<IReservesRepository, ReservesRepository>();
            services.AddScoped<ISponsorEntreeRepository, SponsorEntreeRepository>();
            services.AddScoped<IAdhesionCatalogRepository, AdhesionCatalogRepository>();
            services.AddScoped<IAdhesionOrderRepository, AdhesionOrderRepository>();
            services.AddScoped<IFiliereRepository, FiliereRepository>();
            services.AddScoped<ILanguageRepository, LanguageRepository>();
            services.AddScoped<IZoneRepository, ZoneRepository>();
            services.AddScoped<IFloorRepository, FloorRepository>();
            services.AddScoped<ISectionRepository, SectionRepository>();
            services.AddScoped<IOperatorRepository, OperatorRepository>();
            services.AddScoped<IHomeModularBlockEmplacementRepository, HomeModularBlockEmplacementRepository>();
            services.AddScoped<IHomeModularBlockUserConfigRepository, HomeModularBlockUserConfigRepository>();
            services.AddScoped<IOpinionOrderRepository, OpinionOrderRepository>();
            services.AddScoped<ISaleChannelRepository, SaleChannelRepository>();
            services.AddScoped<IAlotissementRepository, AlotissementRepository>();
            services.AddScoped<IGateRepository, GateRepository>();
            services.AddScoped<ITribuneRepository, TribuneRepository>();
            services.AddScoped<IContingentRepository, ContingentRepository>();
            services.AddScoped<IDenominationRepository, DenominationRepository>();
            services.AddScoped<IConsumerRepository, ConsumerRepository>();
            services.AddScoped<IDepotVenteParamRepository, DepotVenteParamRepository>();
            services.AddScoped<IDepotVenteRepository, DepotVenteRepository>();
            services.AddScoped<IDepotVenteStatutRepository, DepotVenteStatutRepository>();
            services.AddScoped<ISkipQueueRepository, SkipQueueRepository>();
            services.AddScoped<IProprietesReferencesOfManifsRepository, ProprietesReferencesOfManifsRepository>();
            services.AddScoped<IDossierSvgRepository, DossierSvgRepository>();
            services.AddScoped<IDossierProduitRepository, DossierProduitRepository>();
            services.AddScoped<IDossierProduitReservationRepository, DossierProduitReservationRepository>();
            services.AddScoped<ICommandeLigneCompRepository, CommandeLigneCompRepository>();
            services.AddScoped<IEntreeSvgRepository, EntreeSvgRepository>();
            services.AddScoped<IEntreeComplementRepository, EntreeComplementRepository>();
            services.AddScoped<IGPManifestationRepository, GPManifestationRepository>();

            services.AddScoped<IManifestationInfosRepository, ManifestationInfosRepository>();            
            services.AddScoped<IManifestationImageRepository, ManifestationImageRepository>();
            services.AddScoped<IManifestationImagesWebRepository, ManifestationImagesWebRepository>();
            services.AddScoped<IImagesWebRepository, ImagesWebRepository>();

            services.AddScoped<IGPSeanceRepository, GPSeanceRepository>();
            services.AddScoped<ICibleRepository, CibleRepository>();
            services.AddScoped<ISeanceCibleRepository, SeanceCibleRepository>();
            services.AddScoped<IOfferRepository, OfferRepository>();
            services.AddScoped<ILowerPriceEventRepository, LowerPriceEventRepository>();
            services.AddScoped<ITypeTarifRepository, TypeTarifRepository>();
            services.AddScoped<IDeviseRepository, DeviseRepository>();
            services.AddScoped<IProducteurRepository, ProducteurRepository>();
            services.AddScoped<IReferenceLieuPhysiqueRepository, ReferenceLieuPhysiqueRepository>();
            services.AddScoped<IPanierProduitRepository, PanierProduitRepository>();
            services.AddScoped<IHistoDossierRepository, HistoDossierRepository>();
            services.AddScoped<IProduitStockRepository, ProduitStockRepository>();
            services.AddScoped<IProduitInternetRepository, ProduitInternetRepository>();
            services.AddScoped<IPanierEntreeRepository, PanierEntreeRepository>();
            services.AddScoped<ICncSeanceInfosRepository, CncSeanceInfosRepository>();
            services.AddScoped<ILocalisationRepository, LocalisationRepository>();
            services.AddScoped<IDisciplineRepository, DisciplineRepository>();
            services.AddScoped<ITraductionManifestationInfosRepository, TraductionManifestationInfosRepository>();
            services.AddScoped<IFormuleAbonnementRepository, FormuleAbonnementRepository>();
            services.AddScoped<IFormuleGroupeRepository, FormuleGroupeRepository>();
            services.AddScoped<IAbonnementManifestationRepository, AbonnementManifestationRepository>();
            services.AddScoped<ILinkAboFermeMaquetteRepository, LinkAboFermeMaquetteRepository>();
         
            

            services.AddScoped<ILowerPriceAboRepository, LowerPriceAboRepository>();
            services.AddScoped<IOffreGestionPlaceRepository, OffreGestionPlaceRepository>();
            services.AddScoped<IPanierEntreeAboRepository, PanierEntreeAboRepository>();
            services.AddScoped<IPanierProduitResaPropsRepository, PanierProduitResaPropsRepository>();
            services.AddScoped<ITvaRepository, TvaRepository>();
            services.AddScoped<ITraductionRepository, TraductionRepository>();
            services.AddScoped<IViewTraductionGPManifestationRepository, ViewTraductionGPManifestationRepository>();
            services.AddScoped<IOffreProfilAcheteurRepository, OffreProfilAcheteurRepository>();
            services.AddScoped<ILieuRepository, LieuRepository>();
            services.AddScoped<IManifestationGroupeRepository, ManifestationGroupeRepository>();
            services.AddScoped<IManifestationSuperGroupeRepository, ManifestationSuperGroupeRepository>();
            services.AddScoped<ITranslateFieldsVariablesRepository, TranslateFieldsVariablesRepository>();
            services.AddScoped<ICatalogRepository, CatalogRepository>();
            services.AddScoped<ICatalogAreaRepository, CatalogAreaRepository>();
            services.AddScoped<ICatalogMainMenuItemRepository, CatalogMainMenuItemRepository>();
            services.AddScoped<ICatalogMainMenuTypeRepository, CatalogMainMenuTypeRepository>();
            services.AddScoped<ICatalogDisplayGroupTypeRepository, CatalogDisplayGroupTypeRepository>();
            services.AddScoped<ICatalogViewModeTypeRepository, CatalogViewModeTypeRepository>();
            services.AddScoped<ICatalogSubMenuTypeRepository, CatalogSubMenuTypeRepository>();
            services.AddScoped<ICatalogDisplaySettingRepository, CatalogDisplaySettingRepository>();
            services.AddScoped<ICatalogDestinationPageRepository, CatalogDestinationPageRepository>();
            services.AddScoped<ICatalogSortGroupTypeRepository, CatalogSortGroupTypeRepository>();
            services.AddScoped<ICatalogFilterTypeRepository, CatalogFilterTypeRepository>();
            services.AddScoped<ITstActiveDirectoryGroupRepository, TstActiveDirectoryGroupRepository>();
            services.AddScoped<ITstRoleRepository, TstRoleRepository>();
            services.AddScoped<ITstModuleRepository, TstModuleRepository>();
            services.AddScoped<ITstAccessRepository, TstAccessRepository>();
            services.AddScoped<ILogsPartnerRepository, LogsPartnerRepository>();
            services.AddScoped<IGestionPlaceReserveRepository, GestionPlaceReserveRepository>();
            services.AddScoped<IOpenPartnerRepository, OpenPartnerRepository>();
            services.AddScoped<IGestionPlaceTypeEnvoiRepository, GestionPlaceTypeEnvoiRepository>();

            #region carnets de tickets
            services.AddScoped<IFeedBookTokenRepository, FeedBookTokenRepository>();
            services.AddScoped<IFeedBookRepository, FeedBookRepository>();
            services.AddScoped<IFeedBookTokenProduitRepository, FeedBookTokenProduitRepository>();
            services.AddScoped<IFeedBookProduitRepository, FeedBookProduitRepository>();
            services.AddScoped<IFeedBookTokenProductGrilleTarifRepository, FeedBookTokenProductGrilleTarifRepository>();
            // Wt:
            services.AddScoped<IPanierEntreeFeedbookTokenRepository, PanierEntreeFeedbookTokenRepository>();



            //services.AddSingleton<IFeedBookTokenProduitTypeTarifsRepository, FeedBookTokenProduitTypeTarifsRepository>();            

            #endregion
            services.AddScoped<IPartenairesRolesRepository, PartenairesRolesRepository>();
            services.AddScoped<IStructuresRepository, StructuresRepository>();
            services.AddScoped<ILibelleTelRepository, LibelleTelRepository>();
            services.AddScoped<IInfoCompRepository, InfoCompRepository>();
            services.AddScoped<IConnectionStructureDatabaseInfoRepository, ConnectionStructureDatabaseInfoRepository>();
            services.AddScoped<IConnexionswebTracingOfStructureRepository,ConnexionswebTracingOfStructureRepository>();
            services.AddScoped<ITstLoginRepository, TstLoginRepository>();
            services.AddScoped<IConnexionsLoginRepository, ConnexionsLoginRepository>();
            services.AddScoped<IActionsReferenceToLogRepository, ActionsReferenceToLogRepository>();
            services.AddScoped<IActionsConnexionsRepository, ActionsConnexionsRepository>();
            services.AddScoped<IActionsReferenceToLogRepository, ActionsReferenceToLogRepository>();
            services.AddScoped<IConnexionsLogsRepository, ConnexionsLogsRepository>();
            services.AddScoped<IParamsDeflagRepository, ParamsDeflagRepository>();
            services.AddScoped<ICouponsPromoRepository, CouponsPromoRepository>();
            services.AddScoped<ILieuPhysiqueRepository, LieuPhysiqueRepository>();

            services.AddScoped<IIdentiteImmatriculationRepository, IdentiteImmatriculationRepository>();
            services.AddScoped<IPartnerIdsProviderRepository, PartnerIdsProviderRepository>();
            services.AddScoped<IPartnerRodrigueIdCombineRepository, PartnerRodrigueIdCombineRepository>();
            services.AddScoped<IPartnerConnectionRepository, PartnerConnectionRepository>();
            services.AddScoped<ILinkAboFermeMaquetteRepository, LinkAboFermeMaquetteRepository>();
            services.AddScoped<IFormuleAbonnementRepository,  FormuleAbonnementRepository>();
            services.AddScoped<IAbonnementManifestationRepository, AbonnementManifestationRepository>();
            services.AddScoped<IMaquetteRepository, MaquetteRepository>();
            services.AddScoped<IQueuingRepository, QueuingRepository>();
            services.AddScoped<IQueuingHistopassagesRepository, QueuingHistopassagesRepository>();

            services.AddScoped<IProduitFamilleRepository, ProduitFamilleRepository>();
            services.AddScoped<IProduitLienSousFamilleRepository, ProduitLienSousFamilleRepository>();
            services.AddScoped<IProduitSousFamilleRepository, ProduitSousFamilleRepository>();
            services.AddScoped<IProduitDescriptifLongRepository, ProduitDescriptifLongRepository>();
            services.AddScoped<IProduitDroitBoutiqueRepository, ProduitDroitBoutiqueRepository>();
            services.AddScoped<ILinkProduitInternetTypeEnvoiRepository, LinkProduitInternetTypeEnvoiRepository>();

            services.AddScoped<IJaugesGroupeRepository, JaugesGroupeRepository>();
            services.AddScoped<IJaugesRepository, JaugesRepository>();
            services.AddScoped<IJaugesGroupeTarifRepository, JaugesGroupeTarifRepository>();
        }

        /// <summary>
        /// Extention method for add rodrigue managers in <see cref="IServiceCollection"/>
        /// </summary>
        /// <param name="services"></param>
        public static void AddRodrigueManager(this IServiceCollection services)
        {
            services.AddSingleton<RodrigueNLogger>();
            services.AddScoped<IRazorViewRenderService, RazorViewRenderService>();
            services.AddScoped<LoginService>();


            services.AddTransient<IJsonSettingsService, JsonSettingsService>();
            services.AddTransient<IPassCultureService, PassCultureService>();
            services.AddTransient<IRevenderOrderManagementService, RevenderOrderManagementService>();

            services.AddTransient<IAccessControlManager, AccessControlManager>();
            services.AddTransient<IAdhesionCatalogManager, AdhesionCatalogManager>();
            services.AddTransient<IAdhesionOrdersManager, AdhesionOrdersManager>();
            services.AddTransient<IActionPaiementManager, ActionPaiementManager>();
            services.AddTransient<IBasketManager, BasketManager>();
            services.AddTransient<IBuyerProfilManager, BuyerProfilManager>();
            services.AddTransient<ICompteClientManager, CompteClientManager>();
            services.AddTransient<IDossierManager, DossierManager>();
            services.AddTransient<IRodrigueConfigIniDictionnary, RodrigueConfigIniDictionnary>();
            
            services.AddTransient<IEventManager, EventManager>();
            services.AddTransient<IFiliereManager, FiliereManager>();
            
            services.AddTransient<IWebUserManager, WebUserManager>();
            services.AddTransient<IDemandPassWordResetManager, DemandPassWordResetManager>();
            

            services.AddTransient<IGestionTraceManager, GestionTraceManager>();
            services.AddTransient<IGestionPlaceManager, GestionPlaceManager>();
            services.AddTransient<IIdentiteManager, IdentiteManager>();
            services.AddTransient<IGlobalAppellationManager, GlobalAppellationManager>();

            services.AddTransient<IInsuranceManager, InsuranceManager>();
            services.AddTransient<IInsurancesContractsManager, InsurancesContractsManager>();
            services.AddTransient<IInfoCompManager, InfoCompManager>();
            services.AddTransient<IOperatorManager, OperatorManager>();
            services.AddTransient<IFiliereManager, FiliereManager>();
            services.AddTransient<IDeviseManager, DeviseManager>();
            services.AddTransient<IMaquetteManager, MaquetteManager>();
            services.AddTransient<IOrderInfoManager, OrderInfoManager>();
            services.AddTransient<IOrderLineManager, OrderLineManager>();
            services.AddTransient<IOrderManager, OrderManager>();
            services.AddTransient<IPartnerManager, PartnerManager>();
            services.AddTransient<IPaymentMethodManager, PaymentMethodManager>();
            services.AddTransient<IPriceManager, PriceManager>();
            services.AddTransient<IPrintHomeManager, PrintHomeManager>();
            services.AddTransient<IProductManager, ProductManager>();
            services.AddTransient<IReservesManager, ReservesManager>();
            services.AddTransient<ISeatManager, SeatManager>();
            services.AddTransient<ISessionManager, SessionManager>();
            services.AddTransient<ISponsorManager, SponsorManager>();
            services.AddTransient<IStructuresManager, StructuresManager>();
            services.AddTransient<IStructurePrefsManager, StructurePrefsManager>();
            services.AddTransient<ITranslateManager, TranslateManager>();
            services.AddTransient<IValeurTarifStockManager, ValeurTarifStockManager>();
            services.AddTransient<IUnidyManager, UnidyManager>();
            services.AddTransient<IHomeModularManager, HomeModularManager>();
            services.AddTransient<IAdhesionOrdersManager, AdhesionOrdersManager>();
            services.AddTransient<IStructurePrefsManager, StructurePrefsManager>();
            services.AddTransient<IInsurancesContractsManager, InsurancesContractsManager>();
            services.AddTransient<ISponsorManager, SponsorManager>();
            services.AddTransient<IPartnerManager, PartnerManager>();
            services.AddTransient<ISoftwareAGManager, SoftwareAGManager>();
            services.AddTransient<IExportManager, ExportManager>();
            services.AddTransient<IOpinionOrderManager, OpinionOrderManager>();
            services.AddTransient<ISaleChannelManager, SaleChannelManager>();
            services.AddTransient<IEventGenreManager, EventGenreManager>();
            services.AddTransient<IPlaceObjectManager, PlaceObjectManager>();
            services.AddTransient<IAccessManager, AccessManager>();
            services.AddTransient<IConsumerManager, ConsumerManager>();
            services.AddTransient<ISkipQueueManager, SkipQueueManager>();
            services.AddTransient<IProprietesOfManifsManager, ProprietesOfManifsManager>();
            services.AddTransient<ILanguageManager, LanguageManager>();
            services.AddTransient<IWsAdminStructuresManager, WsAdminStructuresManager>();
            services.AddTransient<IThemisSupportToolsManager, ThemisSupportToolsManager>();

            services.AddTransient<IEntreeComplementManager, EntreeComplementManager>();
            services.AddTransient<IEnvoiPdfdepuisOpenManager, EnvoiPdfdepuisOpenManager>();
            services.AddTransient<IDepotVenteManager, DepotVenteManager>();
            services.AddTransient<IAbonnementManager, AbonnementManager>();
            services.AddTransient<IOfferManager, OfferManager>();
            services.AddTransient<ITraductionManager, TraductionManager>();
            services.AddTransient<ICatalogManager, CatalogManager>();
            services.AddTransient<ILogsPartnerManager, LogsPartnerManager>();
            
            services.AddTransient<IFeedBooksManager, FeedBooksManager>();
            services.AddTransient<IFeedBookTokensManager, FeedBookTokensManager>();
            services.AddTransient<ITranslationsPlatformsManager, TranslationsPlatformsManager>();

            services.AddTransient<ICacheSHelper, CacheSHelper>();
            

            services.AddTransient<IOpenPartnerManager, OpenPartnerManager>();
            services.AddTransient<IQueuingManager, QueuingManager>();


            // services.AddSingleton<IFeedBooksTarifsManager, FeedBooksTarifsManager>();
            services.AddTransient<ICategoryManager, CategoryManager>();
            services.AddTransient<IEventGroupManager, EventGroupManager>();
            services.AddTransient<IEventSuperGroupManager, EventSuperGroupManager>();
            services.AddScoped<IJaugesManager, JaugesManager>();

            services.AddScoped<IPdfService, PdfService>();

            // services.AddSingleton<IFeedBooksTarifsManager, FeedBooksTarifsManager>();

        }

        /// <summary>
        /// Extention method for add rodrigue automapper profiles in <see cref="IServiceCollection"/>
        /// </summary>
        /// <param name="services"></param>
        public static void AddRodrigueMapper(this IServiceCollection services)
        {
            var configMap = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile(new ApiExternalProfiles());
                cfg.AddProfile(new EventSessionProfiles());
                cfg.AddProfile(new OrderProfiles());
                cfg.AddProfile(new ExportEditionProfiles());
                cfg.AddProfile(new WsAdminProfiles());
                cfg.AddProfile(new HomeModularProfiles());
                cfg.AddProfile(new BoutiqueProfiles());
                cfg.AddProfile(new TranslationProfiles());
                cfg.AddProfile(new WebTracingProfiles());
                cfg.AddProfile(new ThemisSupportToolsProfiles());
                cfg.AddProfile(new CustomerAreaProfiles());
                cfg.AddProfile(new CatalogProfiles());
                cfg.AddProfile(new PassCultureProfiles());
                cfg.AddProfile(new QueuingProfiles());
            });

            services.AddTransient(sp => configMap.CreateMapper());
        }


        public static void AddRodrigueRedis(this IServiceCollection services, string connectionString, string instanceName)
        {
            string redisConnectionString = CryptoHelper.DecryptRedisConnectionString(connectionString);

            services.AddStackExchangeRedisCache(options =>
            {
                options.Configuration = redisConnectionString;
                options.InstanceName = instanceName;
            });

            services.AddSingleton<IConnectionMultiplexer>(sp =>
                    ConnectionMultiplexer.Connect(redisConnectionString));
        }

        public static void InitHelpers(this IServiceCollection services)
        {
            // Enregistrement des services Razor et autres
            services.AddControllersWithViews();
            services.AddHttpContextAccessor();
            services.AddMvcCore().AddRazorViewEngine();

            services.AddSingleton<DiagnosticListener>(new DiagnosticListener("NomPersonnalisé"));

            services.AddScoped<IRazorViewToStringRenderer, RazorViewToStringRenderer>();

            services.AddSingleton<DiagnosticSource, DiagnosticListener>(provider =>
                new DiagnosticListener("UnitTestDiagnostic"));

            // Aucun .BuildServiceProvider() ici ❌
        }


        public static void ConfigureHelpers(this IServiceCollection services)
        {
            var builder = services.BuildServiceProvider();

            DicoHelper.Configure(
                builder.GetRequiredService<ITranslateManager>(),
                builder.GetRequiredService<IConfiguration>()
            );

            StructureInfosHelper.Configure(
                builder.GetRequiredService<ILanguageRepository>(),
                builder.GetRequiredService<IStructureRepository>()
            );

            UrlHelper.Configure(
                builder.GetRequiredService<IConfiguration>(),
                builder.GetRequiredService<IRodrigueConfigIniDictionnary>()
            );

            RodrigueConfigIniHelper.Configure(builder.GetRequiredService<IRodrigueConfigIniDictionnary>());

            IPHelper.Configure();
        }

        public static void ConfigureHelpers(this IServiceProvider provider)
        {
            DicoHelper.Configure(
                provider.GetRequiredService<ITranslateManager>(),
                provider.GetRequiredService<IConfiguration>()
            );

            StructureInfosHelper.Configure(
                provider.GetRequiredService<ILanguageRepository>(),
                provider.GetRequiredService<IStructureRepository>()
            );

            UrlHelper.Configure(
                provider.GetRequiredService<IConfiguration>(),
                provider.GetRequiredService<IRodrigueConfigIniDictionnary>()
            );

            EditionViewHelper.Configure(
                provider.GetRequiredService<IPartnerManager>(),
                provider.GetRequiredService<ISeatManager>(),
                provider.GetRequiredService<IOrderManager>(),
                provider.GetRequiredService<IPrintHomeManager>(),
                provider.GetRequiredService<ITranslateManager>(),
                provider.GetRequiredService<IGestionTraceManager>(),
                provider.GetRequiredService<IRazorViewToStringRenderer>()
            );

            RodrigueConfigIniHelper.Configure(
                provider.GetRequiredService<IRodrigueConfigIniDictionnary>()
            );

        }
    }
}