using Core.Themis.Libraries.Data.Entities.Common;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Core.Themis.Libraries.Data.Entities.WSAdmin
{
    [Table("revendeurs")]
    public class RevendeurEntity : GenericEntity
    {
        [Key]
        [Column("id")]
        public int Id { get; set; }

        [Column("name")]
        public string Name { get; set; } = string.Empty;
    }
}
