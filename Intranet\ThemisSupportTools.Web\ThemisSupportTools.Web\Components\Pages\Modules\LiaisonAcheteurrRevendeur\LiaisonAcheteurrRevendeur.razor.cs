﻿using Microsoft.AspNetCore.Components;
using Core.Themis.Libraries.BLL.Services.Access.Interfaces;

namespace ThemisSupportTools.Web.Components.Pages.Modules.LiaisonAcheteurrRevendeur
{
    public partial class LiaisonAcheteurrRevendeur
    {
        [Inject] public IPartnerManager PartnerManager { get; set; } = default!;
        [Inject] public IWsAdminStructuresManager WsAdminStructuresManager { get; set; } = default!;
        [Inject] public IStringLocalizer<Resource> Localizer { get; set; } = default!;
        [Inject] public ITstAccessService TstAccessService { get; set; } = default!;
        [Inject] public NavigationManager NavigationManager { get; set; } = default!;
        [Parameter] public int? StructureId { get; set; }

        private const string MODULE_NAME = "liaison-acheteurr-revendeur";

        // États du composant
        private bool IsLoading = true;
        private bool IsSaving = false;
        private string ErrorMessage = string.Empty;
        private bool HasDatabaseConnectionIssue = false;
        private LiaisonStep CurrentStep = LiaisonStep.SelectRevendeur;

        // Données
        private List<BuyerProfilDTO>? Revendeurs;
        private List<BuyerProfilDTO>? ProfilsAcheteursLies;
        private List<BuyerProfilDTO>? TousProfilsAcheteurs;
        private List<WsAdminStructureDTO>? Structures;

        // Sélections
        private BuyerProfilDTO? SelectedRevendeur;
        private BuyerProfilDTO? SelectedProfilAcheteur;
        private BuyerProfilDTO? SelectedStructureProfilAcheteur;
        private WsAdminStructureDTO? SelectedStructure;
        private string SelectedStructureId = string.Empty;
        private string SelectedStructureForDisplay => SelectedStructure?.Name ?? string.Empty;

        // Énumération des étapes
        public enum LiaisonStep
        {
            SelectRevendeur,
            SelectProfilAcheteur,
            SelectStructureAndProfilAcheteur,
            Confirmation
        }

        protected override async Task OnInitializedAsync()
        {
            try
            {
                // Vérifier l'autorisation d'accès au module (si le service est initialisé)
                if (TstAccessService.TstAccess != null)
                {
                    if (!TstAccessService.IsGranted(MODULE_NAME))
                    {
                        NavigationManager.NavigateTo("");
                        return;
                    }
                }
                // Si TstAccess est null, on continue sans vérification d'autorisation
                // (cela peut arriver en développement ou si le middleware d'auth n'est pas configuré)

                await LoadInitialData();
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Erreur lors du chargement initial : {ex.Message}";
            }
            finally
            {
                IsLoading = false;
                StateHasChanged();
            }
        }

        private async Task LoadInitialData()
        {
            // Charger tous les revendeurs de toutes les structures
            Revendeurs = await PartnerManager.GetAllRevendeursAsync();

            // Charger les structures disponibles pour la sélection ultérieure
            Structures = await WsAdminStructuresManager.GetActivesWsAdminStructuresAsync();

            // Vérifier si la liste est vide, ce qui peut indiquer un problème de connexion
            HasDatabaseConnectionIssue = Revendeurs?.Count == 0;
        }

        private async Task SelectRevendeur(BuyerProfilDTO revendeur)
        {
            try
            {
                SelectedRevendeur = revendeur;
                IsLoading = true;
                StateHasChanged();

                // Charger les profils acheteurs liés à ce revendeur depuis sa structure d'origine
                if (revendeur.StructureId.HasValue)
                {
                    ProfilsAcheteursLies = await PartnerManager.GetProfilsAcheteursLiesAuRevendeurAsync(revendeur.StructureId.Value, revendeur.Id);
                }

                CurrentStep = LiaisonStep.SelectProfilAcheteur;
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Erreur lors de la sélection du revendeur : {ex.Message}";
            }
            finally
            {
                IsLoading = false;
                StateHasChanged();
            }
        }



        private void SetSelectedProfilAcheteur(BuyerProfilDTO profil)
        {
            SelectedProfilAcheteur = profil;
            StateHasChanged();
        }

        private async Task ProceedToStructureSelection()
        {
            if (SelectedProfilAcheteur == null)
            {
                ErrorMessage = "Veuillez sélectionner un profil acheteur.";
                return;
            }

            CurrentStep = LiaisonStep.SelectStructureAndProfilAcheteur;
            StateHasChanged();
        }

        private async Task SelectStructure(WsAdminStructureDTO structure)
        {
            try
            {
                SelectedStructure = structure;
                IsLoading = true;
                StateHasChanged();

                // Charger les profils acheteurs de la structure sélectionnée
                var structureId = int.Parse(structure.StructureId);
                TousProfilsAcheteurs = await PartnerManager.GetProfilsAcheteursDeStructureAsync(structureId);
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Erreur lors du chargement des profils de la structure : {ex.Message}";
            }
            finally
            {
                IsLoading = false;
                StateHasChanged();
            }
        }

        private void SetSelectedStructureProfilAcheteur(BuyerProfilDTO profil)
        {
            SelectedStructureProfilAcheteur = profil;
            StateHasChanged();
        }

        private async Task SaveLiaison()
        {
            if (SelectedRevendeur == null || SelectedProfilAcheteur == null || SelectedStructureProfilAcheteur == null || SelectedStructure == null)
            {
                ErrorMessage = "Veuillez sélectionner tous les éléments requis.";
                return;
            }

            try
            {
                IsSaving = true;
                StateHasChanged();

                var structureId = int.Parse(SelectedStructure.StructureId);

                // Vérifier si la liaison existe déjà
                var exists = await PartnerManager.LiaisonExistsAsync(structureId, SelectedRevendeur.Id, SelectedStructureProfilAcheteur.Id);
                if (exists)
                {
                    ErrorMessage = "Cette liaison existe déjà.";
                    return;
                }

                // Créer la liaison dans la table revendeur_is_profil_acheteur_on_structure
                // Cette liaison connecte :
                // - Le revendeur sélectionné (étape 1)
                // - Le profil acheteur du revendeur sélectionné (étape 2)
                // - Le profil acheteur de la structure sélectionné (étape 3)
                // - La structure sélectionnée (étape 3)
                var liaison = new RevendeurIsProfilAcheteurOnStructureDTO
                {
                    RevendeurId = SelectedRevendeur.Id,                    // Revendeur de l'étape 1
                    StructureId = structureId,                            // Structure sélectionnée
                    ProfilAcheteurId = SelectedStructureProfilAcheteur.Id, // Profil de la structure (étape 3)
                    DateOperation = DateTime.Now
                };

                var success = await PartnerManager.SaveLiaisonRevendeurProfilAcheteurStructureAsync(liaison);
                if (success)
                {
                    CurrentStep = LiaisonStep.Confirmation;
                }
                else
                {
                    ErrorMessage = "Erreur lors de la sauvegarde de la liaison.";
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Erreur lors de la sauvegarde : {ex.Message}";
            }
            finally
            {
                IsSaving = false;
                StateHasChanged();
            }
        }

        private void GoBackToRevendeurs()
        {
            CurrentStep = LiaisonStep.SelectRevendeur;
            SelectedRevendeur = null;
            SelectedProfilAcheteur = null;
            SelectedStructureProfilAcheteur = null;
            SelectedStructure = null;
            ProfilsAcheteursLies = null;
            TousProfilsAcheteurs = null;
            StateHasChanged();
        }

        private void GoBackToProfilsAcheteurs()
        {
            CurrentStep = LiaisonStep.SelectProfilAcheteur;
            SelectedProfilAcheteur = null;
            SelectedStructureProfilAcheteur = null;
            SelectedStructure = null;
            TousProfilsAcheteurs = null;
            StateHasChanged();
        }

        private void GoBackToStructureSelection()
        {
            CurrentStep = LiaisonStep.SelectStructureAndProfilAcheteur;
            SelectedStructureProfilAcheteur = null;
            TousProfilsAcheteurs = null;
            StateHasChanged();
        }

        private void StartNewLiaison()
        {
            CurrentStep = LiaisonStep.SelectRevendeur;
            SelectedRevendeur = null;
            SelectedProfilAcheteur = null;
            SelectedStructureProfilAcheteur = null;
            SelectedStructure = null;
            ProfilsAcheteursLies = null;
            TousProfilsAcheteurs = null;
            ErrorMessage = string.Empty;
            StateHasChanged();
        }

        private void ClearError()
        {
            ErrorMessage = string.Empty;
            StateHasChanged();
        }

        private void GoBackToProfilsAcheteurs()
        {
            CurrentStep = LiaisonStep.SelectProfilAcheteur;
            SelectedStructureProfilAcheteur = null;
            TousProfilsAcheteurs = null;
            StateHasChanged();
        }

        private void StartNewLiaison()
        {
            CurrentStep = LiaisonStep.SelectRevendeur;
            SelectedRevendeur = null;
            SelectedProfilAcheteur = null;
            SelectedStructureProfilAcheteur = null;
            SelectedStructure = null;
            ProfilsAcheteursLies = null;
            TousProfilsAcheteurs = null;
            ErrorMessage = string.Empty;
            StateHasChanged();
        }

        private void ClearError()
        {
            ErrorMessage = string.Empty;
            StateHasChanged();
        }


    }
}
