﻿using AutoMapper;
using Core.Themis.Libraries.BLL.Interfaces;
using Core.Themis.Libraries.BLL.WsAdmin.Interfaces;
using Core.Themis.Libraries.Data.Entities.WsAdmin.Partner;
using Core.Themis.Libraries.Data.Repositories.WSAdmin.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.BuyerProfil.Interfaces;
using Core.Themis.Libraries.Data.Repositories.Open.Partner.Interfaces;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.Catalog.Filter;
using Core.Themis.Libraries.DTO.Catalog.Models;
using Core.Themis.Libraries.DTO.Lookup;
using Core.Themis.Libraries.DTO.WSAdmin;
using Core.Themis.Libraries.Utilities.Extensions;
using Core.Themis.Libraries.Utilities.Logging;
using Core.Themis.Libraries.Utilities.ThemisSql;
using Microsoft.AspNetCore.Components;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.Extensions.Caching.Memory;
using Parlot.Fluent;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Linq.Expressions;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Core.Themis.Libraries.BLL
{
    public class PartnerManager : IPartnerManager
    {
        private static readonly RodrigueNLogger Logger = new();


        private readonly IMapper _mapper;
        private readonly IPartnerRepository _partnerRepository;
        private readonly IStructuresRepository _structuresRepository;
        private readonly IPartenairesRolesRepository _partenairesRolesRepository;
        private readonly IMemoryCache _memoryCache;
        private readonly IBuyerProfilRepository _buyerProfilRepository;
        private readonly IRevendeurIsProfilAcheteurOnStructureRepository _revendeurProfilRepository;
        private readonly IWsAdminStructuresManager _wsAdminStructuresManager;

        public PartnerManager(IMapper mapper, IPartnerRepository partnerRepository, IStructuresRepository structuresRepository,
            IPartenairesRolesRepository partenairesRolesRepository, IMemoryCache memoryCache,
            IBuyerProfilRepository buyerProfilRepository, IRevendeurIsProfilAcheteurOnStructureRepository revendeurProfilRepository,
            IWsAdminStructuresManager wsAdminStructuresManager)
        {
            _mapper = mapper;
            _partnerRepository = partnerRepository;
            _structuresRepository = structuresRepository;
            _partenairesRolesRepository = partenairesRolesRepository;
            _memoryCache = memoryCache;
            _buyerProfilRepository = buyerProfilRepository;
            _revendeurProfilRepository = revendeurProfilRepository;
            _wsAdminStructuresManager = wsAdminStructuresManager;
        }

        public async Task<IEnumerable<string>> GetStructuresLinkedOfPartnerAsync(int partnerId)
        {
            string cacheKey = $"GetStructuresLinkedOfPartnerAsync_{partnerId}";

            object? cacheResult = _memoryCache.Get(cacheKey);
            if (cacheResult is not null)
            {
                //    return (IEnumerable<string>)Task.FromResult(cacheResult);
                return cacheResult as IEnumerable<string>;

            }

            var result = await _partnerRepository.GetStructuresLinkedOfPartnerAsync(partnerId);


            _memoryCache.Set(cacheKey!, result);


            return result;
        }

        public string GenerateRandomPassword()
        {
            // Generate random password  as a secret key
            int length = 25;
            string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+";
            var random = new Random();
            string password = new string(Enumerable.Repeat(chars, length)
                .Select(s => s[random.Next(s.Length)]).ToArray());

            return password;
        }


        /// <summary>
        /// liste de tous les partenaires
        /// </summary>
        /// <param name="typeRun">Type e base de données DEV, TEST, PROD</param>
        /// <param name="wsAdminCnx">connexion de WsAdmin</param>
        /// <param name="scriptPathSqlCommons">Chemin vers les fichiers SQL</param>
        /// <returns></returns>
        public static List<PartnerDTO> GetPartnersList(string typeRun, SqlConnection wsAdminCnx, string scriptPathSqlCommons)
        {
            try
            {
                List<PartnerDTO> listPs = new List<PartnerDTO>();
                //using (SqlConnection cnxAdmin = DBFunctions.ConnectWSAdmin(typeRun).getCnx())
                using (wsAdminCnx)
                {
                    // string sqlPass = "SELECT partenaire_id, password FROM partenaires WHERE partenaire_nom =@PartName AND (date_supp is null OR date_supp<getdate())";
                    string partnerSql = FilesForSqlRequestsManager.GetScript(0, "Partner\\", "getPartnersList", scriptPathSqlCommons);


                    if (wsAdminCnx.State == ConnectionState.Closed)
                        wsAdminCnx.Open();
                    using (SqlCommand cmd = new SqlCommand())
                    {
                        cmd.CommandText = partnerSql;

                        cmd.Connection = wsAdminCnx;

                        using (SqlDataReader reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {

                                PartnerDTO part = new PartnerDTO()
                                {
                                    PartnerId = reader.GetInt32(reader.GetOrdinal("partenaire_id")),
                                    PartnerName = reader.GetString(reader.GetOrdinal("partenaire_nom")),

                                    SecretKey = reader.GetString(reader.GetOrdinal("secretKey"))
                                };
                                if (reader["password"] != System.DBNull.Value)
                                    part.Password = (string)(reader["password"]);


                                if (reader["date_supp"] != System.DBNull.Value)
                                    part.DateSupp = (DateTime)(reader["date_supp"]);

                                listPs.Add(part);
                            }
                        }
                    }

                }

                return listPs;
            }
            catch (Exception ex)
            {
                Logger.Error(0, ex, "GetPartnersList exception!");

                Exception ex2 = new Exception("GetPartnersList exception!");
                throw ex2;
            }
            finally
            {
                if (wsAdminCnx.State == ConnectionState.Open)
                    wsAdminCnx.Close();

            }
        }



        /// <summary>
        /// liste des structures auquelles à droit le partenaire
        /// </summary>
        /// <param name="partner"></param>
        /// <param name="wsAdminCnx">connexion de WsAdmin</param>
        /// <param name="scriptPathSqlCommons">Chemin vers les fichiers SQL</param>
        /// <returns></returns>
        public List<WsAdminStructureDTO> GetStructuresListOfPartner(PartnerDTO partner, SqlConnection wsAdminCnx, string scriptPathSqlCommons)
        {
            string cacheKey = $"GetStructuresListOfPartner_{partner}";
            var cacheResult = _memoryCache.Get<List<WsAdminStructureDTO>>(cacheKey);

            if (cacheResult is not null)
                return cacheResult;

            try
            {
                partner.LstStructuresLinked = new();

                //if (wsAdminCnx.State == ConnectionState.Closed)
                //    wsAdminCnx.Open();

                string partnerRoleSql = FilesForSqlRequestsManager.GetScript(0, "Partner\\", "getStructuresOfPartner", scriptPathSqlCommons);

                using (SqlCommand cmd = new SqlCommand())
                {
                    cmd.CommandText = partnerRoleSql;
                    SqlParameter sqlPpass = new SqlParameter("@partnerId", partner.PartnerId);
                    cmd.Parameters.Add(sqlPpass);
                    cmd.Connection = wsAdminCnx;

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            if (reader.IsDBNull(0) == false)
                            {
                                int strucId = Convert.ToInt32(reader["structure_id"].ToString());
                                string strucName = reader["name"].ToString();
                                WsAdminStructureDTO structure = new()
                                {
                                    Name = strucName,
                                    StructureId = strucId.ToString()
                                };

                                partner.LstStructuresLinked.Add(structure);
                            }
                        }
                    }
                }
                //wsAdminCnx.Close();
                //wsAdminCnx.Dispose();
                var offset = DateTimeOffset.UtcNow.AddMilliseconds(500);

                _memoryCache.Set(cacheKey, partner.LstStructuresLinked, offset);
                return partner.LstStructuresLinked;
            }
            catch (Exception ex)
            {
                Logger.Error(0, ex, "GetStructuresListOfPartner exception!");
                Exception ex2 = new Exception("GetStructuresListOfPartner exception!");
                throw ex2;
            }
            finally
            {
                //if (wsAdminCnx.State == ConnectionState.Open)
                //    wsAdminCnx.Close();
                //wsAdminCnx.Dispose();
            }
        }

        /// <summary>
        /// liste des roles pour un partenaire
        /// </summary>
        /// <param name="partner"></param>
        /// <param name="wsAdminCnx">connexion de WsAdmin</param>
        /// <param name="scriptPathSqlCommons">Chemin vers les fichiers SQL</param>
        /// <returns></returns>
        public List<PartnerRoleDTO> GetRolesListOfPartner(PartnerDTO partner, SqlConnection wsAdminCnx, string scriptPathSqlCommons)
        {
            string cacheKey = $"GetRolesListOfPartner{partner}";
            try
            {
                partner.LstRolesOfPartner = new List<PartnerRoleDTO>();

                //if (wsAdminCnx.State == ConnectionState.Closed)
                //    wsAdminCnx.Open();
                string partnerRoleSql = FilesForSqlRequestsManager.GetScript(0, "Partner\\", "getPartnerRoles", scriptPathSqlCommons);

                using (SqlCommand cmd = new SqlCommand())
                {
                    cmd.CommandText = partnerRoleSql;
                    SqlParameter sqlPpass = new SqlParameter("@partnerId", partner.PartnerId);
                    cmd.Parameters.Add(sqlPpass);
                    cmd.Connection = wsAdminCnx;

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            if (reader.IsDBNull(0) == false)
                            {

                                PartnerRoleDTO partnerRole = new PartnerRoleDTO
                                {
                                    PartnerRoleId = reader.GetInt32(reader.GetOrdinal("partner_role_id")),
                                    PartnerRoleCode = reader.GetString(reader.GetOrdinal("partner_role_code"))
                                };

                                partner.LstRolesOfPartner.Add(partnerRole);
                            }
                        }
                    }
                }
                //wsAdminCnx.Close();
                //wsAdminCnx.Dispose();
                var offset = DateTimeOffset.UtcNow.AddMilliseconds(500);

                _memoryCache.Set<List<int>>(cacheKey, partner.LstRolesOfPartner.Select(lrp => lrp.PartnerRoleId).ToList());

                return partner.LstRolesOfPartner;
            }
            catch (Exception ex)
            {
                Logger.Error(0, ex, "GetRolesListOfPartner exception!");
                Exception ex2 = new Exception("GetRolesListOfPartner exception!");
                throw ex2;
            }
            finally
            {
                //if (wsAdminCnx.State == ConnectionState.Open)
                //    wsAdminCnx.Close();
                //wsAdminCnx.Dispose();
            }
        }


        #region create/update/delete


        /// <summary>
        /// creation du lien partenaire "a droit d'acces" sur une structure
        /// </summary>
        /// <param name="partnerId"></param>
        /// <param name="structureId"></param>
        /// <param name="wsAdminCnx">connexion de WsAdmin</param>
        /// <param name="scriptPathSqlCommons">Chemin vers les fichiers SQL</param>
        /// <returns></returns>
        public static bool CreatePartnerLink(int partnerId, int structureId, SqlConnection wsAdminCnx, string scriptPathSqlCommons)
        {
            try
            {
                bool succ = false;

                if (wsAdminCnx.State == ConnectionState.Closed)
                    wsAdminCnx.Open();
                string createPartnerStructureLinksql = FilesForSqlRequestsManager.GetScript(0, "Partner\\", "createPartnerStructureLink", scriptPathSqlCommons);

                using (SqlCommand cmd = new SqlCommand())
                {
                    cmd.Parameters.Add(new SqlParameter("@partnerId", partnerId));
                    cmd.Parameters.Add(new SqlParameter("@structureId", structureId.ToString("0000")));
                    cmd.CommandText = createPartnerStructureLinksql;
                    cmd.Connection = wsAdminCnx;

                    int result = (int)cmd.ExecuteNonQuery();
                    if (result == 1)
                        succ = true;

                }
                wsAdminCnx.Close();
                wsAdminCnx.Dispose();
                return succ;
            }
            catch (Exception ex)
            {
                Logger.Error(0, ex, "CreatePartnerLink exception!");
                Exception ex2 = new Exception("CreatePartnerLink exception!");
                throw ex2;
            }
            finally
            {
                if (wsAdminCnx.State == ConnectionState.Open)
                    wsAdminCnx.Close();
                //wsAdminCnx.Dispose();
            }
        }



        /// <summary>
        /// creation du lien partenaire "a droit d'acces" sur une structure
        /// </summary>
        /// <param name="partnerId">Id du partenaire</param>
        /// <param name="structureId">Id de la structure</param>
        /// <param name="wsAdminCnx">connexion de WsAdmin</param>
        /// <param name="scriptPathSqlCommons">Chemin vers les fichiers SQL</param>
        /// <returns></returns>
        public static bool DeletePartnerLink(int partnerId, int structureId, SqlConnection wsAdminCnx, string scriptPathSqlCommons)
        {
            try
            {
                bool succ = false;

                if (wsAdminCnx.State == ConnectionState.Closed)
                    wsAdminCnx.Open();
                string createPartnerStructureLinksql = FilesForSqlRequestsManager.GetScript(0, "Partner\\", "deletePartnerStructureLink", scriptPathSqlCommons);

                using (SqlCommand cmd = new SqlCommand())
                {
                    cmd.Parameters.Add(new SqlParameter("@partnerId", partnerId));
                    cmd.Parameters.Add(new SqlParameter("@structureId", structureId.ToString("0000")));
                    cmd.CommandText = createPartnerStructureLinksql;
                    cmd.Connection = wsAdminCnx;

                    int result = (int)cmd.ExecuteNonQuery();
                    if (result == 1)
                        succ = true;

                }

                return succ;
            }
            catch (Exception ex)
            {
                Exception ex2 = new Exception("DeletePartnerLink exception!");
                throw ex2;
            }
            finally
            {
                if (wsAdminCnx.State == ConnectionState.Open)
                    wsAdminCnx.Close();
                // wsAdminCnx.Dispose();
            }
        }

        /// <summary>
        /// creation du partenaire
        /// </summary>
        /// <param name="part">Objet du partenaire à créer</param>
        /// <param name="wsAdminCnx">connexion de WsAdmin</param>
        /// <param name="scriptPathSqlCommons">Chemin vers les fichiers SQL</param>
        /// <returns></returns>
        public int CreatePartner(PartnerDTO part)
        {
            try
            {
                var parttneEntity = _mapper.Map<PartenairesEntity>(part);
                var newpartnerId = _partnerRepository.Insert(parttneEntity, true, false);
                return newpartnerId;
            }
            catch (Exception)
            {

                throw;
            }
            
        }
        public async Task<int> UpdatePartenaireRolesAsync(int partid, List<int> roleIds)
        {
            var result = await _partnerRepository.UpdateRolesLinkedAsync(partid, roleIds);
            //_memoryCache.Remove($"GetPartnerInfosById_{partid}");
            //_memoryCache.Remove($"GetRolesByPartnerId_{partid}");

            return result;
        }


        public int UpdatePartenaireRoles(int partid, List<int> roleIds)
        {
            //Transactionnals begin

            ///////////// delete all roles
            ///////////// add all roles
            ///

            //trans commit

            var rolesLinked = _partnerRepository.UpdateRolesLinked(partid, roleIds);
            //_memoryCache.Remove($"GetPartnerInfosById_{partid}");
            //_memoryCache.Remove($"GetRolesListOfPartner_{roleIds}");
            return rolesLinked;

        }

        public async Task<int> UpdatePartenaireStructuresAsync(int partid, List<string> structuresIds)
        {
            var structuresLinked = await _partnerRepository.UpdateStructuresLinkedAsync(partid, structuresIds);
            _memoryCache.Remove($"GetPartnerInfosByIdWithDependancyAsync{partid}");
            //_memoryCache.Remove($"GetStructuresByPartnerId_{partid}");


            return structuresLinked;
        }
        public int UpdatePartenaireStructures(int partid, List<string> structuresIds)
        {
          
            //            _memoryCache.Remove($"GetAllCatalogsByStructureId_{catalog.StructureId}");
            var structuresLinked = _partnerRepository.UpdateStructuresLinked(partid, structuresIds);
            _memoryCache.Remove($"GetPartnerInfosById_{partid}");
            _memoryCache.Remove($"GetStructuresByPartnerId_{partid}");
            _memoryCache.Remove($"GetPartnerInfosByIdWithDependancy_{partid}");


            _memoryCache.RemoveGetterGenericCache(typeof(PartenairesEntity));
            _memoryCache.RemoveGetterGenericCache(typeof(StructureAcceptPartenaireEntity));
            _memoryCache.RemoveGetterGenericCache(typeof(PartenairesRolesEntity));

            //_partnerRepository.RemoveGetterCache();






            return structuresLinked;



        }

        //    try
        //    {
        //        int result = 0;

        //        if (wsAdminCnx.State == ConnectionState.Closed)
        //            wsAdminCnx.Open();
        //        string partnerCreatesql = FilesForSqlRequestsManager.GetScript(0, "Partner\\", "createPartner", scriptPathSqlCommons);

        //        using (SqlCommand cmd = new SqlCommand())
        //        {
        //            var equals = new List<string>();

        //            int i = 0;

        //            cmd.Parameters.Add(new SqlParameter("@partnerName", part.PartnerName));
        //            if (string.IsNullOrEmpty(part.SecretKey))
        //            {
        //                part.SecretKey = Utils.RandomString(15);
        //            }
        //            cmd.Parameters.Add(new SqlParameter("@secretKey", part.SecretKey));

        //            if (!string.IsNullOrEmpty(part.Password))
        //            {
        //                part.Password = Utils.RandomString(15);
        //            }
        //            cmd.Parameters.Add(new SqlParameter("@password", part.Password));

        //            //string command = string.Format(partnerRoleSql, string.Join(", ", equals.ToArray()));

        //            cmd.CommandText = partnerCreatesql;


        //            cmd.Connection = wsAdminCnx;

        //            result = (int)cmd.ExecuteScalar();

        //        }


        //        return result;
        //    }
        //    catch (Exception ex)
        //    {
        //        Exception ex2 = new Exception("CreatePartner exception!");
        //        throw ex2;
        //    }
        //    finally
        //    {
        //        if (wsAdminCnx.State == ConnectionState.Open)
        //            wsAdminCnx.Close();
        //        //   wsAdminCnx.Dispose();
        //    }


        public async Task<bool> UpdatePartnerAsync(PartnerDTO part)
        {
            var entity = _mapper.Map<PartenairesEntity>(part);
            var partnerUpdated = await _partnerRepository.UpdateAsync(entity);
            _memoryCache.Remove($"GetPartner_{part.PartnerId}");

            _memoryCache.RemoveGetterGenericCache(typeof(PartenairesEntity));


            return partnerUpdated;
        }

        /// <summary>
        /// mise à jour du partenaire
        /// </summary>
        /// <param name="partId">Id du partenaire</param>
        /// <param name="part">Objet du partenaire à mettre à jour</param>
        /// <param name="wsAdminCnx">connexion de WsAdmin</param>
        /// <param name="scriptPathSqlCommons">Chemin vers les fichiers SQL</param>
        /// <returns>retourne un entier pour savoir si la mise à jour à été effectuée</returns>
        public async Task<bool> UpdatePartnerWithDependanciesAsync(PartnerDTO part)
        {
            var entity = _mapper.Map<PartenairesEntity>(part);

            try
            {
                bool resultUpdate = await _partnerRepository.UpdateAsync(entity);
                if (resultUpdate)
                {
                    int result = await _partnerRepository.UpdateRolesLinkedAsync(part.PartnerId, part.LstRolesOfPartner.Select(r => r.PartnerRoleId).ToList());

                    //si aucune structure, on supprime la liaison
                    if (part.LstStructuresLinked.Count == 0)
                        await _partnerRepository.DeleteAllStructuresLinkedAsync(part.PartnerId);
                    else
                        await _partnerRepository.UpdateStructuresLinkedAsync(part.PartnerId, part.LstStructuresLinked.Select(s => s.StructureId).ToList());

                    _memoryCache.Remove($"GetPartnerInfosByNameWithDependancyAsync_{part.PartnerName}");

                    _memoryCache.RemoveGetterGenericCache(typeof(PartenairesEntity));
                    _memoryCache.RemoveGetterGenericCache(typeof(StructureAcceptPartenaireEntity));
                    _memoryCache.RemoveGetterGenericCache(typeof(PartenairesRolesEntity));

                }


            }
            catch (Exception)
            {

                throw;
            }

            return true;

            //try
            //{
            //    int result = 0;

            //    if (wsAdminCnx.State == ConnectionState.Closed)
            //        wsAdminCnx.Open();
            //    string updatePartnersql = FilesForSqlRequestsManager.GetScript(0, "Partner\\", "updatePartner", scriptPathSqlCommons);

            //    using (SqlCommand cmd = new SqlCommand())
            //    {
            //        var equals = new List<string>();

            //        int i = 0;

            //        if (!string.IsNullOrEmpty(part.PartnerName))
            //        {
            //            var pn = "@sp" + i.ToString();
            //            equals.Add(string.Format("{0}={1}", "[partenaire_nom]", pn));
            //            cmd.Parameters.Add(new SqlParameter(pn, part.PartnerName));
            //            i++;
            //        }
            //        if (!string.IsNullOrEmpty(part.Password))
            //        {
            //            var pn = "@sp" + i.ToString();
            //            equals.Add(string.Format("{0}={1}", "[password]", pn));
            //            cmd.Parameters.Add(new SqlParameter(pn, part.Password));
            //            i++;
            //        }


            //        string command = string.Format(updatePartnersql, string.Join(", ", equals.ToArray()));

            //        cmd.CommandText = command;
            //        SqlParameter sqlPpass = new SqlParameter("@partnerId", part.PartnerId);
            //        cmd.Parameters.Add(sqlPpass);
            //        cmd.Connection = wsAdminCnx;

            //        result = cmd.ExecuteNonQuery();

            //    }


            //    return result;

        }
        //catch (Exception ex)
        //{
        //    Exception ex2 = new Exception("UpdatePartner exception!");
        //    throw ex2;
        //}
        //finally
        //{
        //    if (wsAdminCnx.State == ConnectionState.Open)
        //        wsAdminCnx.Close();
        //    // wsAdminCnx.Dispose();
        //}


        #endregion

        /// <summary>
        /// obtenir le partenaire avec infos et roles avec le nom
        /// </summary>
        /// <remarks>ne devrait être appellé que par _widgetUtilities.GetPartnerIdAndRoles pour gestion du cache !</remarks>
        /// <param name="name">Nom du partenaire</param>
        /// <param name="wsAdminCnx">connexion de WsAdmin</param>
        /// <param name="scriptPathSqlCommons">Chemin vers les fichiers SQL</param>
        /// <returns></returns>
        public PartnerDTO GetPartnerIdAndRoles(string name, SqlConnection wsAdminCnx, string scriptPathSqlCommons, string whoiam)
        {
            try
            {
                PartnerDTO partner = new PartnerDTO();

                string GetPartnerIdAndRolesCacheName = $"GetPartnerIdAndRoles_[{name}.{wsAdminCnx.ConnectionString}]";
                string cacheKey = GetPartnerIdAndRolesCacheName;

                var cacheResult = _memoryCache.Get<PartnerDTO>(cacheKey);
                if (cacheResult is not null)
                    return cacheResult;

                //Crypt le mot de passe 
                //string encryptPassWord = ThemisBCrypt.GetBcrypt(password);
                int partId = 0;


                if (wsAdminCnx.State == ConnectionState.Closed)
                    wsAdminCnx.Open();
                // string sqlPass = "SELECT partenaire_id, password FROM partenaires WHERE partenaire_nom =@PartName AND (date_supp is null OR date_supp<getdate())";
                string partnerSql = $"/*{whoiam}*/" + FilesForSqlRequestsManager.GetScript(0, "Partner\\", "getPartnerId", scriptPathSqlCommons);

                //log.Debug("partnerSql : " + partnerSql);
                string passwordDB = "";
                string secretKey = "";

                using (SqlCommand cmd = new SqlCommand())
                {
                    cmd.CommandText = partnerSql;
                    SqlParameter sqlPpass = new SqlParameter("partnerName", name);
                    cmd.Parameters.Add(sqlPpass);
                    cmd.Connection = wsAdminCnx;

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        if (reader.Read()) // Don't assume we have any rows.
                        {
                            partId = reader.GetInt32(reader.GetOrdinal("partenaire_id"));
                            passwordDB = reader["password"] == System.DBNull.Value ? "" : reader["password"].ToString().Trim();
                            secretKey = reader["secretKey"] == System.DBNull.Value ? "" : reader["secretKey"].ToString().Trim();
                        }
                    }
                }

                //if (!ThemisBCrypt.DoesPasswordMatch(passwordDB, password))

                if (partner == null)
                {
                    Exception ex = new UnauthorizedAccessException("name/password is not valid");
                    throw ex;
                }
                else
                {
                    partner.PartnerName = name;
                    partner.PartnerId = partId;
                    partner.SecretKey = secretKey;
                    partner.LstStructuresLinked = this.GetStructuresListOfPartner(partner, wsAdminCnx, scriptPathSqlCommons);
                    partner.LstRolesOfPartner = this.GetRolesListOfPartner(partner, wsAdminCnx, scriptPathSqlCommons);
                }
                var offset = DateTimeOffset.UtcNow.AddMilliseconds(5000);

                _memoryCache.Set<PartnerDTO>(cacheKey, partner);
                return partner;
            }
            catch (Exception ex)
            {
                Logger.Error(0, ex, "GetPartnerIdAndRoles exception!");
                Exception ex2 = new Exception("GetPartnerIdAndRoles exception!");
                throw ex2;
            }
            finally
            {
                if (wsAdminCnx.State == ConnectionState.Open)
                    wsAdminCnx.Close();
            }
        }

        /// <summary>
        /// obtenir le partenaire avec  et roles avec l'id
        /// </summary>
        /// <param name="partenaireId">Id du partenaire</param>
        /// <param name="wsAdminCnx">connexion de WsAdmin</param>
        /// <param name="scriptPathSqlCommons">Chemin vers les fichiers SQL</param>
        /// <returns></returns>
        public PartnerDTO GetPartner(int partenaireId, SqlConnection wsAdminCnx, string scriptPathSqlCommons, string whoiam)
        {
            string cacheKey = $"GetPartner_{partenaireId}";

            var cacheResult = _memoryCache.Get<PartnerDTO>(cacheKey);
            if (cacheResult is not null)
                return cacheResult;



            try
            {
                PartnerDTO partner = new PartnerDTO();


                if (wsAdminCnx.State == ConnectionState.Closed)
                    wsAdminCnx.Open();
                //string sql = "SELECT password AS salt FROM partenaires "
                //  + "WHERE partenaire_id =@partenaireId";

                string sql = $"/* {whoiam} */" + FilesForSqlRequestsManager.GetScript(0, "Partner\\", "getPartner", scriptPathSqlCommons);

                // log.Debug("Getpartner : sql " + sql);
                using (SqlCommand cmd = new SqlCommand())
                {
                    cmd.CommandText = sql;
                    SqlParameter sqlPpass = new SqlParameter("@partnerId", partenaireId);
                    cmd.Parameters.Add(sqlPpass);
                    cmd.Connection = wsAdminCnx;

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        if (reader.Read()) // Don't assume we have any rows.
                        {
                            partner.PartnerId = reader.GetInt32(reader.GetOrdinal("partenaire_id"));
                            partner.PartnerName = reader.GetString(reader.GetOrdinal("partenaire_nom"));
                            partner.Password = reader.GetString(reader.GetOrdinal("password"));
                            partner.SecretKey = reader.GetString(reader.GetOrdinal("salt"));
                        }
                    }


                    partner.LstStructuresLinked = this.GetStructuresListOfPartner(partner, wsAdminCnx, scriptPathSqlCommons);
                    partner.LstRolesOfPartner = GetRolesListOfPartner(partner, wsAdminCnx, scriptPathSqlCommons);

                }
                var offset = DateTimeOffset.UtcNow.AddMilliseconds(5000);

                _memoryCache.Set<PartnerDTO>(cacheKey, partner, offset);

                return partner;

            }
            catch (Exception ex)
            {

                throw new Exception(ex.Message);
            }
            finally
            {
                if (wsAdminCnx.State == ConnectionState.Open)
                    wsAdminCnx.Close();


            }

        }


        #region gestion des tokens
        /// <summary>
        /// genere l'objet ClaimsIdentity qui contient le nom,  droits et structures du partenaire
        /// </summary>
        /// <param name="partner"></param>
        /// <returns></returns>
        public static ClaimsIdentity GenerateClaimIdentity(PartnerDTO partner)
        {

            ClaimsIdentity claimsIdentity = new ClaimsIdentity(new[]
            {
                new Claim(ClaimTypes.Name, partner.PartnerId.ToString()),
               // new Claim(ClaimTypes.Role, "PowerUser"),
               // new Claim(ClaimTypes.Role, "Admin")
            });


            if (partner.LstRolesOfPartner == null)
                partner.LstRolesOfPartner = new List<PartnerRoleDTO>();

            foreach (PartnerRoleDTO role in partner.LstRolesOfPartner)
            {
                claimsIdentity.AddClaim(new Claim(ClaimTypes.Role, role.PartnerRoleCode));
            }

            if (partner.LstStructuresLinked == null)
                partner.LstStructuresLinked = new();

            if (partner.LstRolesOfPartner.Where(s => s.PartnerRoleCode == "Rodrigue").Count() > 0)
            {
                // operateur Rodrigue !
                claimsIdentity.AddClaim(new Claim(ClaimTypes.UserData, "*"));
            }
            else
            {

                foreach (WsAdminStructureDTO s in partner.LstStructuresLinked)
                {
                    if (int.TryParse(s.StructureId, out int structureIdInt))
                        {
                        claimsIdentity.AddClaim(new Claim(ClaimTypes.UserData, structureIdInt.ToString()));
                    }
                    else
                    {

                        claimsIdentity.AddClaim(new Claim(ClaimTypes.UserData, s.StructureId));
                    }
                    
                }
            }

            return claimsIdentity;
        }


        /// <summary>
        /// genere l'objet ClaimsIdentity qui contient le nom,  droits et structures du partenaire
        /// </summary>
        /// <param name="partner"></param>
        /// <returns></returns>
        public static ClaimsIdentity GenerateClaimIdentity(PartenairesEntity partner)
        {

            ClaimsIdentity claimsIdentity = new ClaimsIdentity(new[]
            {
                new Claim(ClaimTypes.Name, partner.PartenaireId.ToString()),
               // new Claim(ClaimTypes.Role, "PowerUser"),
               // new Claim(ClaimTypes.Role, "Admin")
            });


            if (partner.PartenairesRoles == null)
                partner.PartenairesRoles = new List<PartenairesRolesEntity>();

            foreach (PartenairesRolesEntity role in partner.PartenairesRoles)
            {
                claimsIdentity.AddClaim(new Claim(ClaimTypes.Role, role.Code));
            }

            //            if (partner.StructureAcceptPartenaires == null)
            //              partner.StructureAcceptPartenaires = new(); ??

            if (partner.PartenairesRoles.Where(s => s.Code == "Rodrigue").Count() > 0)
            {
                // operateur Rodrigue !
                claimsIdentity.AddClaim(new Claim(ClaimTypes.UserData, "*"));
            }
            else
            {

                foreach (StructureAcceptPartenaireEntity s in partner.StructureAcceptPartenaires)
                {
                    if (int.TryParse(s.StructureId, out int structureIdInt))
                    {
                        claimsIdentity.AddClaim(new Claim(ClaimTypes.UserData, structureIdInt.ToString()));
                    }
                    else
                    {

                        claimsIdentity.AddClaim(new Claim(ClaimTypes.UserData, s.StructureId));
                    }
                }
            }

            return claimsIdentity;
        }

        #endregion

        #region Liste des partenaires 
        /// <summary>
        /// récupération des Partenaires
        /// 
        /// en base de données 
        /// </summary>
        /// <returns></returns>

        public List<PartnerDTO> GetAllPartners()
        {
            //var partenairesInDB = _partnerRepository.GetPartenaires();



            var partenairesInDB = _partnerRepository.GetAllCustomEntities()
                                        .Include(p => p.StructureAcceptPartenaires)
                                        .Include(p => p.PartenairesRoles)
                                        .Where(s => s.DateSupp == null)
                                        .ToEntityList();

            List<PartnerDTO> partners = _mapper.Map<List<PartnerDTO>>(partenairesInDB);
            partners.ForEach(p =>
            {
                p.LstStructuresLinkedForSelect2 = p.LstStructuresLinked.Select(s => s.Name).ToArray();
                p.LstRolesOfPartnerForSelect2 = p.LstRolesOfPartner.Select(r => r.PartnerRoleCode).ToArray();
            });

            return partners;
        }

        /// <summary>
        /// Liste des structures de WsAdmin active (non supprimée)
        /// </summary>
        /// <returns></returns>
        public List<PartnerDTO> GetAcivePartnersOrderByName()
        {


            var partnersInDB = _partnerRepository.GetAllCustomEntities()
                                        .Include(p => p.StructureAcceptPartenaires)
                                        .Include(p => p.PartenairesRoles)
                                        .Where(s => s.DateSupp == null)
                                        .ToEntityList();



           // var partnersInDB = _partnerRepository.GetByIdWithDependancy();
            List<PartnerDTO> partners = _mapper.Map<List<PartnerDTO>>(partnersInDB);

            return partners.OrderBy(o => o.PartnerName).ToList();
        }

        /// <summary>
        /// Liste des rôles en base de données 
        /// très utile pour les tests unitaires relatifs au Partner Manager
        /// à noter que n'existe pas (encore ) de manager pour les rôles 
        /// 
        /// </summary>
        /// <returns></returns>
        public List<PartnerRoleDTO> GetRoles()
        {

            var rolesInDB = _partenairesRolesRepository.GetAll();
            List<PartnerRoleDTO> roles = _mapper.Map<List<PartnerRoleDTO>>(rolesInDB);

            return roles.OrderBy(r => r.PartnerRoleId).ToList();
        }
        public List<PartnerRoleDTO> GerRolesByPartnerId(int partnerId)
        {
            string cacheKey = $"GetRolesByPartnerId_{partnerId}";

            var cacheResult = _memoryCache.Get<List<PartnerRoleDTO>>(cacheKey);

            if (cacheResult is not null)
                return cacheResult;
            try
            {
                var rolesByPartnerInDB = _partnerRepository.GetById(partnerId)?
               .PartenairesRoles?.ToList();

                List<PartnerRoleDTO> dto = _mapper.Map<List<PartnerRoleDTO>>(rolesByPartnerInDB);


                return dto;
            }
            catch
            {
                throw;
            }
        }


        public async Task<List<WsAdminStructureDTO>> GetStructuresAcceptPartenairesAsync()
        {
            var structuresAcceptPartenairesInDB = await _structuresRepository.GetAllAsync();
            IEnumerable<WsAdminStructureDTO> structures = _mapper.Map<IEnumerable<WsAdminStructureDTO>>(structuresAcceptPartenairesInDB);

            return structures.OrderBy(s => s.Name).ToList();
        }

        /// <summary>
        /// Liste de toutes les  structures  en base de données 
        /// très utilie pour les tests unitaires relatifs au Partner Manager
        /// à noter que n'existe pas (encore ) de manager pour les structures 
        /// 
        /// </summary>
        /// <returns></returns>
        public List<WsAdminStructureDTO> GetStructuresAcceptPartenaires()
        {
            var structuresAcceptPartenairesInDB = _structuresRepository.GetAll();
            IEnumerable<WsAdminStructureDTO> structures = _mapper.Map<IEnumerable<WsAdminStructureDTO>>(structuresAcceptPartenairesInDB);

            return structures.OrderBy(s => s.Name).ToList();
        }
        /// <summary>
        ///
        /// </summary>
        /// <param name="searchTerm"></param>
        /// <returns></returns>
        public List<WsAdminStructureDTO> GetStructuresByPartnerId(int partnerId)
        {
            string cacheKey = $"GetStructuresByPartnerId_{partnerId}";

            var cacheResult = _memoryCache.Get<List<WsAdminStructureDTO>>(cacheKey);

            if (cacheResult is not null)
                return cacheResult;

            try
            {
                var structuresByPartnerInDB = _partnerRepository.GetById(partnerId)?
               .StructuresAssociated?.ToList();

                List<WsAdminStructureDTO> dto = _mapper.Map<List<WsAdminStructureDTO>>(structuresByPartnerInDB);


                return dto;
            }
            catch
            {
                throw;
            }

        }
        //public async Task<List<PartnerDTO>> SearchPartnersAsync(string searchTerm)
        //{
        //    string cacheKey = $"SearchPartnersAsync{searchTerm}";

        //    var cacheResult = _memoryCache.GetViaExterne<List<PartnerDTO>>(cacheKey);

        //    if (cacheResult is not null)
        //        return cacheResult;

        //    var partnersInDB = await _partnerRepository.GetPartenairesAsync().ConfigureAwait(false); ;

        //    if (!string.IsNullOrWhiteSpace(searchTerm))
        //        partnersInDB = partnersInDB.Where(p => p.PartenaireNom.ToUpper().Contains(searchTerm.ToUpper())).ToList();

        //    var dto = _mapper.Map<List<PartnerDTO>>(partnersInDB);
        //    var offset = DateTimeOffset.UtcNow.AddMilliseconds(500);

        //    _memoryCache.Set(cacheKey, dto, offset);
        //    return dto;

        //}
        #endregion
        #region New GET

        public PartnerDTO? GetPartnerInfosById(int partnerId)
        {
            string cacheKey = $"GetPartnerInfosById_{partnerId}";

            var cacheResult = _memoryCache.Get<PartnerDTO>(cacheKey);

            if (cacheResult is not null)
                return cacheResult;

            try
            {
                var entity = _partnerRepository.GetById(partnerId);
                var dto = _mapper.Map<PartnerDTO>(entity);
                var offset = DateTimeOffset.UtcNow.AddMilliseconds(5000);



                _memoryCache.Set(cacheKey, dto, offset);

                return dto;

            }
            catch
            {
                throw;
            }
        }

        public async Task<PartnerDTO?> GetPartnerInfosByIdAsync(int partnerId)
        {
            string cacheKey = $"GetPartnerInfosByIdAsync_{partnerId}";

            var cacheResult = _memoryCache.Get<PartnerDTO?>(cacheKey);

            if (cacheResult is not null)
                return cacheResult;
            try
            {
                var entity = await _partnerRepository.GetByIdAsync(partnerId)
                                                     .ConfigureAwait(false);
                var dto = _mapper.Map<PartnerDTO>(entity);
                var offset = DateTimeOffset.UtcNow.AddMilliseconds(5000);
                _memoryCache.Set(cacheKey, dto, offset);



                return _mapper.Map<PartnerDTO>(dto);
            }
            catch
            {
                throw;
            }
        }

        public PartnerDTO? GetPartnerInfosByName(string partnerName)
        {
            string cacheKey = $"GetPartnerInfosByName_{partnerName}";
            var cacheResult = _memoryCache.Get<PartnerDTO?>(cacheKey);

            if (cacheResult is not null)
                return cacheResult;
            try
            {
                var entity = _partnerRepository.GetByName(partnerName);
                var dto = _mapper.Map<PartnerDTO>(entity);
                var offset = DateTimeOffset.UtcNow.AddMilliseconds(5000);
                _memoryCache.Set(cacheKey, dto, offset);

                return dto;
            }
            catch
            {
                throw;
            }
        }

        public async Task<PartnerDTO?> GetPartnerInfosByNameAsync(string partnerName)
        {
            //string cacheKey = $"GetPartnerInfosByNameAsync_{partnerName}";
            //var cacheResult = _memoryCache.GetViaExterne<PartnerDTO>(cacheKey);

            //if (cacheResult is not null)
            //{
            //    await Task.CompletedTask;
            //    return cacheResult;
            //}


            try
            {
                var entity = await _partnerRepository.GetByNameAsync(partnerName)
                                                     .ConfigureAwait(false);

                var dto = _mapper.Map<PartnerDTO>(entity);

                var offset = DateTimeOffset.UtcNow.AddMilliseconds(500);
                // _memoryCache.Set(cacheKey, dto, offset);

                return dto;

            }


            catch
            {
                throw;
            }
        }

        public PartnerDTO? GetPartnerInfosByIdWithDependancy(int partnerId, bool withoutDeletedStructure = true)
        {
            string cacheKey = $"GetPartnerInfosByIdWithDependancy_{partnerId}";
            var cacheResult = _memoryCache.Get<PartnerDTO?>(cacheKey);

            if (cacheResult is not null)
                return cacheResult;

            try
            {
                var entity = _partnerRepository.GetByIdWithDependancy(partnerId);

                if (withoutDeletedStructure && entity is not null)
                    entity.StructureAcceptPartenaires = entity.StructureAcceptPartenaires?.Where(sa => sa.Supp is null || sa.Supp != 'O').ToList();

                var offset = DateTimeOffset.UtcNow.AddMilliseconds(500);

                var dto = _mapper.Map<PartnerDTO>(entity);

                _memoryCache.Set<PartnerDTO>(cacheKey, dto, offset);


                return dto;
            }
            catch
            {
                throw;
            }
        }

        public async Task<List<SelectLookup>> GetSelectLookupRolesByPartnerAsync(int partnerId)
        {

            try
            {
                var partner = _partnerRepository.GetByIdWithDependancy(partnerId);
                var roles = await _partenairesRolesRepository.GetAllAsync().ConfigureAwait(false);

                IEnumerable<PartenairesRolesEntity> rolesOfPartner = new List<PartenairesRolesEntity>();
                if (partner is not null)
                    rolesOfPartner = partner.PartenairesRoles;


                return roles.Select(r => new SelectLookup()
                {
                    Value = r.Id.ToString(),
                    Libelle = r.Code,
                    IsSelected = rolesOfPartner.Any(rp => rp.Id == r.Id)
                }).OrderBy(s => s.Libelle).ToList();


            }
            catch
            {
                throw;
            }
        }


        public async Task<List<SelectLookup>> GetSelectLookupStructuresByPartnerAsync(int partnerId)
        {
            try
            {
                var partner = _partnerRepository.GetByIdWithDependancy(partnerId);
                var structures = await _structuresRepository.GetAllAsync().ConfigureAwait(false);

                IEnumerable<StructureAcceptPartenaireEntity> structuresOfPartner = new List<StructureAcceptPartenaireEntity>();
                if (partner is not null)
                    structuresOfPartner = partner.StructureAcceptPartenaires.Where(sap => sap.Supp != 'O');

                return structures.Select(s => new SelectLookup()
                {
                    Value = s.StructureId,
                    Libelle = $"{s.Name}   {s.StructureId}",
                    IsSelected = structuresOfPartner.Any(x => x.StructureId == s.StructureId)
                }).OrderBy(s => s.Libelle).ToList();


            }
            catch
            {
                throw;
            }
        }


        /// <summary>
        /// Retourne la liste des structures actives
        /// </summary>
        /// <param name="partnerId"></param>
        /// <param name="withoutDeletedStructure"></param>
        /// <returns></returns>
        public async Task<List<SelectLookup>> GetSelectLookupStructuresActivesByPartnerAsync(int partnerId, bool withoutDeletedStructure = true)
        {
            try
            {
                var structures = await _structuresRepository.GetAllAsync().ConfigureAwait(false);
                structures = structures?.Where(s => s.Supp != 'O' || s.Supp is not null);

                IEnumerable<StructureAcceptPartenaireEntity> structuresacceptpartenaireVariables = new List<StructureAcceptPartenaireEntity>();
                var partnerEntity = _partnerRepository.GetCustomEntityById(partnerId)
                        .Include(sap => sap.StructureAcceptPartenaires).ToEntity();

                if (structures != null)
                {

                    return structures.Select(s => new SelectLookup()
                    {
                        Value = s.StructureId,
                        Libelle = s.Name,
                        IsSelected = partnerEntity.StructureAcceptPartenaires.Any(sap => sap.StructureId == s.StructureId)
                    }).ToList();

                }

                return new();


                //foreach (var structureAcceptPartenaire in partnerEntity.StructureAcceptPartenaires)
                //{
                //    structureAcceptPartenaire.Structure = _structuresRepository.FindCustomEntityBy( s => s.StructureId == structureAcceptPartenaire.StructureId).ToEntity();
                //}


                //var partner = await _partnerRepository.GetByIdWithDependancyAsync(partnerId)
                //                                     .ConfigureAwait(false);
                //var entities = await _partnerRepository.GetAllAsync().ConfigureAwait(false);

                //IEnumerable<WsAdminStructuresEntity>? wsadminstructures = await _structuresRepository.GetAllAsync().ConfigureAwait((false));

                //if (withoutDeletedStructure && partner is not null)
                //    partner.StructuresAssociated = wsadminstructures.Join(entities?.
                //        Select(
                //        e =>e.StructureAcceptPartenaires?.Where(sa =>sa.Supp is null || sa.Supp != 'O').Select( sa =>sa.StructureId)),
                //         wsadminestructure =>  new {  wsadminestructure.StructureId },


                //, str.StructureAcceptPartenaires?.Where(sa => sa.Supp is null || sa.Supp != 'O').ToList();

                //return _mapper.Map<PartnerDTO>(partnerEntity);
            }
            catch
            {
                throw;
            }
        }

        public PartnerDTO? GetPartnerInfosByNameWithDependancy(string partnerName, bool withoutDeletedStructure = true)
        {
            string cacheKey = $"GetPartnerInfosByNameWithDependancy_{partnerName}";
            var cacheResult = _memoryCache.Get<PartnerDTO>(cacheKey);

            if (cacheResult is not null)
                return cacheResult;
            try
            {
                var entity = _partnerRepository.GetByNameWithDependancy(partnerName);

                if (withoutDeletedStructure && entity is not null)


                    entity.StructureAcceptPartenaires = entity.StructureAcceptPartenaires?.Where(sa => sa.Supp is null || sa.Supp != 'O').ToList();
                var dto = _mapper.Map<PartnerDTO>(entity);

                var offset = DateTimeOffset.UtcNow.AddMilliseconds(500);


                _memoryCache.Set(cacheKey, dto, offset);

                return dto;
            }
            catch
            {
                throw;
            }
        }

        public async Task<PartnerDTO?> GetPartnerInfosByNameWithDependancyAsync(string partnerName, bool withoutDeletedStructure = true)
        {
            string cacheKey = $"GetPartnerInfosByNameWithDependancyAsync_{partnerName}";
            var cacheResult = _memoryCache.Get<PartnerDTO>(cacheKey);
            if (cacheResult is not null)
                return cacheResult;


            try
            {
                var entity = await _partnerRepository.GetByNameWithDependancyAsync(partnerName)
                                                     .ConfigureAwait(false);

                if (withoutDeletedStructure && entity is not null)
                    entity.StructureAcceptPartenaires = entity.StructureAcceptPartenaires?.Where(sa => sa.Supp is null || sa.Supp != 'O').ToList();
                var dto = _mapper.Map<PartnerDTO>(entity);

                var offset = DateTimeOffset.UtcNow.AddMilliseconds(500);
                _memoryCache.Set(cacheKey, dto, offset);

                return dto;
            }
            catch
            {
                throw;
            }
        }


        public async Task DeletePartnerAsync(PartnerDTO partenaireDTO)
        {
            PartenairesEntity partenairesEntity = _mapper.Map<PartenairesEntity>(partenaireDTO);

            //-_partnerRepository.

            await _partnerRepository.DeleteAsync(partenairesEntity!.PartenaireId);

            //var gg = _memoryCache.GetViaExterne($"GetRolesByPartnerId_{partenairesEntity!.PartenaireId}");
            //var ss = _memoryCache.GetViaExterne($"GetStructuresByPartnerId_{partenairesEntity!.PartenaireId}");


            //_memoryCache.Remove($"GetRolesByPartnerId_{partenairesEntity!.PartenaireId}");
            //_memoryCache.Remove($"GetStructuresByPartnerId_{partenairesEntity!.PartenaireId}");
            _memoryCache.RemoveGetterGenericCache(typeof(PartenairesEntity));
           
        }

        public bool DeletePartner(PartnerDTO partenaireDTO)
        {
            PartenairesEntity partenairesEntity = _mapper.Map<PartenairesEntity>(partenaireDTO);

            //-_partnerRepository.

            return _partnerRepository.Delete(partenairesEntity!.PartenaireId);

            //var gg = _memoryCache.GetViaExterne($"GetRolesByPartnerId_{partenairesEntity!.PartenaireId}");
            //var ss = _memoryCache.GetViaExterne($"GetStructuresByPartnerId_{partenairesEntity!.PartenaireId}");


            //_memoryCache.Remove($"GetRolesByPartnerId_{partenairesEntity!.PartenaireId}");
            //_memoryCache.Remove($"GetStructuresByPartnerId_{partenairesEntity!.PartenaireId}");
            _memoryCache.RemoveGetterGenericCache(typeof(PartenairesEntity));
            _memoryCache.RemoveGetterGenericCache(typeof(StructureAcceptPartenaireEntity));
            _memoryCache.RemoveGetterGenericCache(typeof(PartenairesRolesEntity));
        }


        public async Task<int> InsertPartnerAsync(PartnerDTO partenaireDTO)
        {

            PartenairesEntity partenairesEntity = _mapper.Map<PartenairesEntity>(partenaireDTO);

            int newpartnerId = 0;
            try
            {
                newpartnerId = (int)await _partnerRepository.InsertAsync(partenairesEntity, useScopeIdentity:false);




                if (partenaireDTO.LstRolesOfPartner?.Count > 0)
                    await UpdatePartenaireRolesAsync(newpartnerId, partenaireDTO.LstRolesOfPartner?.Select(r => r.PartnerRoleId).ToList());
                //_memoryCache.Remove($"GetRolesByPartnerId_{partenairesEntity!.PartenaireId}");

                if (partenaireDTO.LstStructuresLinked?.Count > 0)
                    await UpdatePartenaireStructuresAsync(newpartnerId, partenaireDTO.LstStructuresLinked.Select(s => s.StructureId).ToList());
                //_memoryCache.Remove($"GetStructuresByPartnerId_{partenairesEntity!.PartenaireId}");


                _memoryCache.RemoveGetterGenericCache(typeof(PartenairesEntity));
                _memoryCache.RemoveGetterGenericCache(typeof(StructureAcceptPartenaireEntity));
                _memoryCache.RemoveGetterGenericCache(typeof(PartenairesRolesEntity));
            }
            catch (Exception ex)
            {
                throw;
            }
            return newpartnerId;

        }

        public int InsertPartner(PartnerDTO partenaireDTO)
        {

            PartenairesEntity partenairesEntity = _mapper.Map<PartenairesEntity>(partenaireDTO);
            int newpartnerId = 0;
            try
            {
                 newpartnerId = _partnerRepository.Insert(partenairesEntity, useScopeIdentity:false);

                if (partenaireDTO.LstRolesOfPartner?.Count > 0)
                    UpdatePartenaireRoles(newpartnerId, partenaireDTO.LstRolesOfPartner?.Select(r => r.PartnerRoleId).ToList());
                //_memoryCache.Remove($"GetRolesByPartnerId_{partenairesEntity!.PartenaireId}");

                if (partenaireDTO.LstStructuresLinked?.Count > 0)
                    UpdatePartenaireStructures(newpartnerId, partenaireDTO.LstStructuresLinked.Select(s => s.StructureId).ToList());
                //_memoryCache.Remove($"GetStructuresByPartnerId_{partenairesEntity!.PartenaireId}");

                _memoryCache.RemoveGetterGenericCache(typeof(PartenairesEntity));
                _memoryCache.RemoveGetterGenericCache(typeof(StructureAcceptPartenaireEntity));
                _memoryCache.RemoveGetterGenericCache(typeof(PartenairesRolesEntity));
            }
            catch (Exception ex)
            {
                throw;
            }
            return newpartnerId;

        }




        public async Task<PartnerDTO?> GetPartnerInfosByIdWithDependancyAsync(int partnerId, bool withoutDeletedStructure = true)
        {
            string cacheKey = $"GetPartnerInfosByIdWithDependancyAsync_{partnerId}_{withoutDeletedStructure}";
            try
            {

                var cacheResult = _memoryCache.Get<PartnerDTO>(cacheKey);
                if (cacheResult is not null)
                    return cacheResult;


                var entity = await _partnerRepository.GetByIdWithDependancyAsync(partnerId);

                if (withoutDeletedStructure && entity is not null)
                    entity.StructureAcceptPartenaires = entity.StructureAcceptPartenaires?.Where(sa => sa.Supp is null || sa.Supp != 'O').ToList();
                var offset = DateTimeOffset.UtcNow.AddMinutes(500);


                var partnerDto = _mapper.Map<PartnerDTO>(entity);

                _memoryCache.Set(cacheKey, partnerDto, offset);
                return partnerDto;
            }
            catch
            {
                throw;
            }
        }



        #endregion

        #region
        public async Task<List<PartnerDTO>> SearchPartnersAsync(string searchTerm)
        {
            var partenairesInDB = await GetActivesPartnersAsync();

            if (!string.IsNullOrWhiteSpace(searchTerm))
                partenairesInDB = partenairesInDB.Where(p => p.PartnerName.ToUpper().Contains(searchTerm.ToUpper()) || p.PartnerId.ToString() == searchTerm).ToList();

            return _mapper.Map<List<PartnerDTO>>(partenairesInDB);
        }

        private async Task<List<PartnerDTO>> GetActivesPartnersAsync()
        {
            var partenairesInDB = await _partnerRepository.GetPartenairesAsync().ConfigureAwait(false);
            List<PartnerDTO> partenaires = _mapper.Map<List<PartnerDTO>>(partenairesInDB.Where(p => p.DateSupp is null));

            return partenaires.OrderBy(o => o.PartnerName).ToList();
        }

        public List<PartnerDTO> GetPartners()
        {
            throw new NotImplementedException();
        }
        #endregion

        public async Task<IEnumerable<dynamic>> GetStructuresWithPartenairesAsync()
        {
            try
            {
                var result = await _partnerRepository.GetStructuresWithPartenairesAsync();
                return result;
            }
            catch (Exception ex)
            {
                Logger.Error(0, ex, "Erreur dans GetStructuresWithPartenairesAsync");
                throw;
            }
        }

        public async Task<List<StructureDTO>> GetStructuresWithPartenairesGroupedAsync()
        {
            try
            {
                var rawData = await _partnerRepository.GetStructuresWithPartenairesAsync();

                if (rawData == null || !rawData.Any())
                {
                    return new List<StructureDTO>();
                }

                // Grouper les données par structure avec la logique métier
                var groupedData = rawData
                    .Where(item => item.structure_id != null && item.structure_name != null)
                    .GroupBy(item => new {
                        structure_id = SafeConvertToInt(item.structure_id),
                        structure_name = SafeConvertToString(item.structure_name)
                    })
                    .Where(group => group.Key.structure_id > 0)
                    .Select(group => new StructureDTO
                    {
                        StructureId = group.Key.structure_id,
                        StructureName = group.Key.structure_name,
                        Partenaires = group
                            .Select(item => SafeConvertToString(item.partenaire_nom))
                            .Where(p => !string.IsNullOrEmpty(p))
                            .Distinct()
                            .Cast<string>()
                            .ToList()
                    })
                    .OrderBy(s => s.StructureId)
                    .ToList();

                return groupedData;
            }
            catch (Exception ex)
            {
                Logger.Error(0, ex, "Erreur dans GetStructuresWithPartenairesGroupedAsync");
                throw;
            }
        }

        private static int SafeConvertToInt(object value)
        {
            if (value == null) return 0;
            if (int.TryParse(value.ToString(), out int result))
                return result;
            return 0;
        }

        public async Task<List<StructureDTO>> GetStructuresWithPartenairesGroupedAsync(List<string> structureIds)
        {
            try
            {
                if (structureIds == null || !structureIds.Any())
                {
                    return new List<StructureDTO>();
                }

                // Récupérer toutes les données groupées
                var allStructuresData = await GetStructuresWithPartenairesGroupedAsync();

                // Filtrer par les IDs de structures demandés
                var filteredData = allStructuresData
                    .Where(structure => structureIds.Contains(structure.StructureId.ToString()))
                    .ToList();

                return filteredData;
            }
            catch (Exception ex)
            {
                Logger.Error(0, ex, "Erreur dans GetStructuresWithPartenairesGroupedAsync avec filtrage");
                throw;
            }
        }

        private static string SafeConvertToString(object value)
        {
            return value?.ToString() ?? string.Empty;
        }

        #region Méthodes pour la liaison revendeur-profil acheteur

        public async Task<List<BuyerProfilDTO>> GetRevendeursAsync(int structureId)
        {
            try
            {
                var profilsAcheteurs = _buyerProfilRepository.GetBuyerProfils(structureId);
                var revendeurs = profilsAcheteurs.Where(p => p.IsRevendeur == true).ToList();
                return _mapper.Map<List<BuyerProfilDTO>>(revendeurs);
            }
            catch (System.Data.SqlClient.SqlException sqlEx)
            {
                Logger.Warn(0, $"Erreur de connexion à la base de données pour la structure {structureId}: {sqlEx.Message}");
                // Retourner une liste vide en cas d'erreur de connexion SQL
                return new List<BuyerProfilDTO>();
            }
            catch (Exception ex)
            {
                Logger.Error(0, ex, $"Erreur dans GetRevendeursAsync pour la structure {structureId}");
                // Retourner une liste vide en cas d'erreur générale
                return new List<BuyerProfilDTO>();
            }
        }

        public async Task<List<BuyerProfilDTO>> GetAllRevendeursAsync()
        {
            try
            {
                var allRevendeurs = new List<BuyerProfilDTO>();
                var structures = await _wsAdminStructuresManager.GetActivesWsAdminStructuresAsync();

                foreach (var structure in structures)
                {
                    try
                    {
                        var structureId = int.Parse(structure.StructureId);
                        var profilsAcheteurs = _buyerProfilRepository.GetBuyerProfils(structureId);
                        var revendeurs = profilsAcheteurs.Where(p => p.IsRevendeur == true).ToList();
                        var revendeursDTO = _mapper.Map<List<BuyerProfilDTO>>(revendeurs);

                        // Ajouter l'information de la structure à chaque revendeur
                        foreach (var revendeur in revendeursDTO)
                        {
                            revendeur.StructureId = structureId;
                            revendeur.StructureName = structure.Name;
                        }

                        allRevendeurs.AddRange(revendeursDTO);
                    }
                    catch (System.Data.SqlClient.SqlException sqlEx)
                    {
                        Logger.Warn(0, $"Erreur de connexion à la base de données pour la structure {structure.StructureId}: {sqlEx.Message}");
                        // Continuer avec les autres structures
                        continue;
                    }
                    catch (Exception ex)
                    {
                        Logger.Error(0, ex, $"Erreur lors du traitement de la structure {structure.StructureId}");
                        // Continuer avec les autres structures
                        continue;
                    }
                }

                return allRevendeurs.OrderBy(r => r.Nom).ToList();
            }
            catch (Exception ex)
            {
                Logger.Error(0, ex, "Erreur dans GetAllRevendeursAsync");
                return new List<BuyerProfilDTO>();
            }
        }

        public async Task<List<BuyerProfilDTO>> GetProfilsAcheteursLiesAuRevendeurAsync(int structureId, int revendeurId)
        {
            try
            {
                var liaisons = await _revendeurProfilRepository.GetLiaisonsByRevendeurAsync(structureId, revendeurId);
                var profilIds = liaisons.Select(l => l.ProfilAcheteurId).ToList();

                var profilsAcheteurs = _buyerProfilRepository.GetBuyerProfils(structureId)
                    .Where(p => profilIds.Contains(p.Id))
                    .ToList();

                return _mapper.Map<List<BuyerProfilDTO>>(profilsAcheteurs);
            }
            catch (System.Data.SqlClient.SqlException sqlEx)
            {
                Logger.Warn(0, $"Erreur de connexion à la base de données pour la structure {structureId}, revendeur {revendeurId}: {sqlEx.Message}");
                // Retourner une liste vide en cas d'erreur de connexion SQL
                return new List<BuyerProfilDTO>();
            }
            catch (Exception ex)
            {
                Logger.Error(0, ex, $"Erreur dans GetProfilsAcheteursLiesAuRevendeurAsync pour revendeur {revendeurId} structure {structureId}");
                // Retourner une liste vide en cas d'erreur générale
                return new List<BuyerProfilDTO>();
            }
        }

        public async Task<List<BuyerProfilDTO>> GetProfilsAcheteursDeStructureAsync(int structureId)
        {
            try
            {
                var profilsAcheteurs = _buyerProfilRepository.GetBuyerProfils(structureId);
                return _mapper.Map<List<BuyerProfilDTO>>(profilsAcheteurs);
            }
            catch (System.Data.SqlClient.SqlException sqlEx)
            {
                Logger.Warn(0, $"Erreur de connexion à la base de données pour la structure {structureId}: {sqlEx.Message}");
                // Retourner une liste vide en cas d'erreur de connexion SQL
                return new List<BuyerProfilDTO>();
            }
            catch (Exception ex)
            {
                Logger.Error(0, ex, $"Erreur dans GetProfilsAcheteursDeStructureAsync pour la structure {structureId}");
                // Retourner une liste vide en cas d'erreur générale
                return new List<BuyerProfilDTO>();
            }
        }

        public async Task<bool> SaveLiaisonRevendeurProfilAcheteurStructureAsync(RevendeurIsProfilAcheteurOnStructureDTO liaison)
        {
            try
            {
                // Vérifier si la liaison existe déjà
                var exists = await _revendeurProfilRepository.LiaisonExistsAsync(liaison.StructureId, liaison.RevendeurId, liaison.ProfilAcheteurId);
                if (exists)
                {
                    Logger.Error(0, $"La liaison existe déjà : Revendeur {liaison.RevendeurId}, Profil {liaison.ProfilAcheteurId}, Structure {liaison.StructureId}");
                    return false;
                }

                var entity = _mapper.Map<Core.Themis.Libraries.Data.Entities.Open.Partner.RevendeurIsProfilAcheteurOnStructureEntity>(liaison);
                entity.DateOperation = DateTime.Now;

                var result = await _revendeurProfilRepository.InsertAsync(liaison.StructureId, entity, null, false);
                return result != null;
            }
            catch (Exception ex)
            {
                Logger.Error(0, ex, $"Erreur dans SaveLiaisonRevendeurProfilAcheteurStructureAsync");
                throw;
            }
        }

        public async Task<bool> LiaisonExistsAsync(int structureId, int revendeurId, int profilAcheteurId)
        {
            try
            {
                return await _revendeurProfilRepository.LiaisonExistsAsync(structureId, revendeurId, profilAcheteurId);
            }
            catch (Exception ex)
            {
                Logger.Error(0, ex, $"Erreur dans LiaisonExistsAsync");
                throw;
            }
        }

        #endregion

    }
}
