using System;

namespace Core.Themis.Libraries.DTO
{
    public class RevendeurIsProfilAcheteurOnStructureDTO
    {
        public int RevendeurId { get; set; }
        public int StructureId { get; set; }
        public int ProfilAcheteurId { get; set; }
        public DateTime DateOperation { get; set; }
        public int OperateurId { get; set; }

        // Propriétés additionnelles pour l'affichage
        public string? RevendeurNom { get; set; }
        public string? StructureNom { get; set; }
        public string? ProfilAcheteurLibelle { get; set; }
    }
}
