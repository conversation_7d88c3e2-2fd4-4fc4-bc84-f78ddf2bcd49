﻿using AutoMapper;
using Core.Themis.Libraries.BLL.Services.Access.Models;
using Core.Themis.Libraries.Data.Entities.LieuPhysique;
using Core.Themis.Libraries.Data.Entities.Open.Abonnement;
using Core.Themis.Libraries.Data.Entities.Open.Lieu;
using Core.Themis.Libraries.Data.Entities.Open.Partner;
using Core.Themis.Libraries.Data.Entities.WsAdmin.Partner;
using Core.Themis.Libraries.Data.Entities.WsAdmin.ThemisSupportTools;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.Enums.ThemisSupportTools;
using Core.Themis.Libraries.DTO.LieuPhysique;
using Core.Themis.Libraries.DTO.LinkAboFermeMaquette;
using Core.Themis.Libraries.DTO.Lookup;
using Core.Themis.Libraries.DTO.ReferenceLieuPhysique;
using Core.Themis.Libraries.DTO.ThemisSupportTools.ConfigIniModels;
using Core.Themis.Libraries.DTO.TST;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Core.Themis.Libraries.BLL.AutoMapperProfiles
{
    public class ThemisSupportToolsProfiles : Profile
    {

        public ThemisSupportToolsProfiles()
        {

            CreateMap<ConfigIniModel, ConfigIniSectionDTO>()
                .ForMember(dest => dest.SectionName, opt => opt.MapFrom(src => src.SectionName))                
                .ForMember(dest => dest.SectionGroupe, opt => opt.MapFrom(src => src.SectionGroupe))                
                .ForMember(dest => dest.SectionFields, opt => opt.MapFrom(src => src.Children));

            CreateMap<ChildModel, ConfigIniSectionFieldDTO>()
                .ForMember(dest => dest.FieldName, opt => opt.MapFrom(src => src.ChildKeyName))
                .ForMember(dest => dest.FieldValue, opt => opt.MapFrom(src => src.ChildKeyValue))
                .ForMember(dest => dest.SelectData, opt => opt.MapFrom(src => SetSelectDatas(src.Attributes)))
                .ForMember(dest => dest.HtmlAttributes, opt => opt.MapFrom(src => SetHtmlAttributes(src.Attributes)))
                //.ForMember(dest => dest.Etat, opt => opt.MapFrom(src => ))
                .ForMember(dest => dest.Min, opt => opt.MapFrom(src => SetIntegerAttribute(src.Attributes, TSTAttributeEnum.Min)))
                .ForMember(dest => dest.Max, opt => opt.MapFrom(src => SetIntegerAttribute(src.Attributes, TSTAttributeEnum.Max)))
                .ForMember(dest => dest.Obsolete, opt => opt.MapFrom(src => src.Attributes.Any(a => a.AttributeType == TSTAttributeEnum.Obsolete)))
                .ForMember(dest => dest.Type, opt => opt.MapFrom(src => src.Attributes.FirstOrDefault(a => a.AttributeType == TSTAttributeEnum.Type)!.AttributeValue))
                .ForMember(dest => dest.Comment, opt => opt.MapFrom(src => src.Attributes.FirstOrDefault(a => a.AttributeType == TSTAttributeEnum.Commentaire)!.AttributeValue))
                .ForMember(dest => dest.IsMandatory, opt => opt.MapFrom(src => src.Attributes.Any(a => a.AttributeType == TSTAttributeEnum.Obligatoire)))
                .ForMember(dest => dest.UseBuyerProfil, opt => opt.MapFrom(src => src.Attributes.Any(a => a.AttributeType == TSTAttributeEnum.UseBuyerProfil)))
                .ForMember(dest => dest.Groupe, opt => opt.MapFrom(src => src.Attributes.FirstOrDefault(a => a.AttributeType == TSTAttributeEnum.Groupe)!.AttributeValue))
                .ForMember(dest => dest.MandatoryMessage, opt => opt.MapFrom(src => src.Attributes.FirstOrDefault(a => a.AttributeType == TSTAttributeEnum.ObligatoireMessage)!.AttributeValue))
                .ForMember(dest => dest.ValuesAuthorize, opt => opt.MapFrom(src => SetAuthorizedValues(src.Attributes)))
                .ForMember(dest => dest.TableName, opt => opt.MapFrom(src => src.Attributes.FirstOrDefault(a => a.AttributeType == TSTAttributeEnum.TableName).AttributeValue))
                .ForMember(dest => dest.TableFieldName, opt => opt.MapFrom(src => src.Attributes.FirstOrDefault(a => a.AttributeType == TSTAttributeEnum.TableFieldName).AttributeValue))
                .ForMember(dest => dest.TableFieldValue, opt => opt.MapFrom(src => src.Attributes.FirstOrDefault(a => a.AttributeType == TSTAttributeEnum.TableFieldValue).AttributeValue))
                .ForMember(dest => dest.IsMultiple, opt => opt.MapFrom(src => src.Attributes.FirstOrDefault(a => a.AttributeType == TSTAttributeEnum.IsMultiple).AttributeValue))
                .ForMember(dest => dest.IsInCustmerXml, opt => opt.MapFrom(src => src.Attributes.FirstOrDefault(a => a.AttributeType == TSTAttributeEnum.IsInCustomerXml).AttributeValue))
                .ForMember(dest => dest.ConnectedToGroup, opt => opt.MapFrom(src => src.Attributes.FirstOrDefault(a => a.AttributeType == TSTAttributeEnum.ConnectedToGroup).AttributeValue));


            CreateMap<TstRoleEntity, TstRoleDTO>()
                .ForMember(dest => dest.RoleId, opt => opt.MapFrom(src => src.RoleId))
                .ForMember(dest => dest.RoleName, opt => opt.MapFrom(src => src.RoleName))
                .ForMember(dest => dest.CanRead, opt => opt.MapFrom(src => src.CanRead))
                .ForMember(dest => dest.CanCreate, opt => opt.MapFrom(src => src.CanCreate))
                .ForMember(dest => dest.CanUpdate, opt => opt.MapFrom(src => src.CanUpdate))
                .ForMember(dest => dest.CanDelete, opt => opt.MapFrom(src => src.CanDelete))
                .ForMember(dest=>dest.Level, opt=>opt.MapFrom(src=>src.Level))
                .ReverseMap();

            CreateMap<TstModuleEntity, TstModuleDTO>()
                .ForMember(dest => dest.ModuleId, opt => opt.MapFrom(src => src.ModuleId))
                .ForMember(dest => dest.ModuleName, opt => opt.MapFrom(src => src.ModuleName))
                .ForMember(dest => dest.ModuleComment, opt => opt.MapFrom(src => src.ModuleComment))
                 .ForMember(dest => dest.TstRoles, opt => opt.MapFrom(src => src.TstRoles))
                .ForMember(dest => dest.TstActiveDirectoryGroups, opt => opt.MapFrom(src => src.TstActiveDirectoryGroups))
                .ReverseMap();

            CreateMap<TstActiveDirectoryGroupEntity, TstActiveDirectoryGroupDTO>()
                .ForMember(dest => dest.ActiveDirectoryGroupId, opt => opt.MapFrom(src => src.ActiveDirectoryGroupId))
                .ForMember(dest => dest.ActiveDirectoryGroupExternalName, opt => opt.MapFrom(src => src.ActiveDirectoryGroupExternalName))
                .ForMember(dest => dest.ActiveDirectoryGroupExternalId, opt => opt.MapFrom(src => src.ActiveDirectoryGroupExternalId))
                .ForMember(dest => dest.TstRoles, opt => opt.MapFrom(src => src.TstRoles))
                .ForMember(dest => dest.TstModules, opt => opt.MapFrom(src => src.TstModules))
                .ReverseMap();


        

            CreateMap<TstModuleEntity, TstModuleModel>()
                .ForMember(dest => dest.ModuleId, opt => opt.MapFrom(src => src.ModuleId))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.ModuleName));

            CreateMap<TstRoleEntity, TstRoleModel>()
                .ForMember(dest => dest.RoleId, opt => opt.MapFrom(src => src.RoleId))
                .ForMember(dest => dest.RoleName, opt => opt.MapFrom(src => src.RoleName))
                .ForMember(dest => dest.CanRead, opt => opt.MapFrom(src => src.CanRead))
                .ForMember(dest => dest.CanCreate, opt => opt.MapFrom(src => src.CanCreate))
                .ForMember(dest => dest.CanUpdate, opt => opt.MapFrom(src => src.CanUpdate))
                .ForMember(dest => dest.CanDelete, opt => opt.MapFrom(src => src.CanDelete))
                .ForMember(dest => dest.Level, opt => opt.MapFrom(src => src.Level));

            CreateMap<TstActiveDirectoryGroupEntity, TstActiveDirectoryGroupModel>()
                .ForMember(dest => dest.ActiveDirectoryGroupId, opt => opt.MapFrom(src => src.ActiveDirectoryGroupId))
                .ForMember(dest => dest.ActiveDirectoryGroupExternalId, opt => opt.MapFrom(src => src.ActiveDirectoryGroupExternalId))
                .ForMember(dest => dest.ActiveDirectoryGroupExternalName, opt => opt.MapFrom(src => src.ActiveDirectoryGroupExternalName));



            CreateMap<TstAccessEntity, TstAccessModel>()
                    .ForMember(dest => dest.TsModuleModel, opt => opt.MapFrom(src => src.TstModule))
                    .ForMember(dest => dest.TstRoleModel, opt => opt.MapFrom(src => src.TstRole))
                    .ForMember(dest => dest.TstActiveDirectoryGroup, opt => opt.MapFrom(src => src.TstActiveDirectoryGroup));

            CreateMap<LieuPhysiqueEntity, LieuPhysiqueDTO>();

            CreateMap<LieuPhysiqueDTO, LieuPhysiqueEntity>();

            CreateMap<ReferenceLieuPhysique, ReferenceLieuPhysiqueDTO>();
            CreateMap<ReferenceLieuPhysiqueDTO, ReferenceLieuPhysique>();

            CreateMap<LinkAboFermeMaquetteEntity, LinkAboFermeMaquetteDTO>()
               .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
               .ForMember(dest => dest.FormuleId, opt => opt.MapFrom(src => src.FormuleId))
               .ForMember(dest => dest.SeanceIdRef, opt => opt.MapFrom(src => src.SeanceIdRef))
               .ForMember(dest => dest.MaquetteId, opt => opt.MapFrom(src => src.MaquetteId))
               .ForMember(dest => dest.DateOperation, opt => opt.MapFrom(src => src.DateOperation));

            CreateMap<LinkAboFermeMaquetteDTO, LinkAboFermeMaquetteEntity>()
               .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
               .ForMember(dest => dest.FormuleId, opt => opt.MapFrom(src => src.FormuleId))
               .ForMember(dest => dest.SeanceIdRef, opt => opt.MapFrom(src => src.SeanceIdRef))
               .ForMember(dest => dest.MaquetteId, opt => opt.MapFrom(src => src.MaquetteId))
               .ForMember(dest => dest.DateOperation, opt => opt.MapFrom(src => src.DateOperation));

            // Mapping pour la liaison revendeur-profil acheteur
            CreateMap<RevendeurIsProfilAcheteurOnStructureEntity, RevendeurIsProfilAcheteurOnStructureDTO>()
               .ReverseMap();
        }

        private static Dictionary<string, object> SetHtmlAttributes(List<AttributeModel> attributes)
        {
            AttributeModel? attributeMin = attributes.FirstOrDefault(at => at.AttributeType == TSTAttributeEnum.Min);
            AttributeModel? attributeMax = attributes.FirstOrDefault(at => at.AttributeType == TSTAttributeEnum.Max);
            AttributeModel? attributeType = attributes.FirstOrDefault(at => at.AttributeType == TSTAttributeEnum.Type);
            AttributeModel? attributeObligatoire = attributes.FirstOrDefault(at => at.AttributeType == TSTAttributeEnum.Obligatoire);
            AttributeModel? attributeObligatoireMessage = attributes.FirstOrDefault(at => at.AttributeType == TSTAttributeEnum.ObligatoireMessage);
            AttributeModel? attributeValeurParDefaut = attributes.FirstOrDefault(at => at.AttributeType == TSTAttributeEnum.ValeurParDefaut);
            AttributeModel? attributeIsInCustomerXml = attributes.FirstOrDefault(at => at.AttributeType == TSTAttributeEnum.IsInCustomerXml);

            // bool isAttributeNumber = attributes.Any(at => at.FieldType == TSTFieldTypeEnum.Number);
            Dictionary<string, object> htmlAttributes = new Dictionary<string, object>();

            if (attributeType != null)
            {
                if (attributeObligatoire != null)
                {
                    htmlAttributes.Add("required", "required");
                    htmlAttributes.Add("invalid-message", attributeObligatoireMessage.AttributeValue);
                }

                if (attributeValeurParDefaut != null)
                {
                    htmlAttributes.Add(attributeValeurParDefaut.AttributeName, attributeValeurParDefaut.AttributeValue);
                }
                if (attributeIsInCustomerXml != null)
                {
                    htmlAttributes.Add(attributeIsInCustomerXml.AttributeName, attributeIsInCustomerXml.AttributeValue);
                }


                htmlAttributes.Add("type", attributeType.AttributeValue);

                switch ((int)attributeType.AttributeType)
                {
                    case (int)TSTFieldTypeEnum.Number:
                        if (attributeMin != null)
                            htmlAttributes.Add("min", attributeMin.AttributeValue);

                        if (attributeMax != null)
                            htmlAttributes.Add("max", attributeMax.AttributeValue);
                        break;

                    case (int)TSTFieldTypeEnum.Text:
                        if (attributeMin != null)
                            htmlAttributes.Add("minlength", attributeMin.AttributeValue);

                        if (attributeMax != null)
                            htmlAttributes.Add("maxlength", attributeMax.AttributeValue);


                        break;


                }
            }

            return htmlAttributes;
        }

        private static List<SelectLookup> SetSelectDatas(List<AttributeModel> attributes)
        {

            AttributeModel? attributeMin = attributes.FirstOrDefault(at => at.AttributeType == TSTAttributeEnum.Min);
            AttributeModel? attributeMax = attributes.FirstOrDefault(at => at.AttributeType == TSTAttributeEnum.Max);

            bool isAttributeSelect = attributes.Any(at => at.FieldType == TSTFieldTypeEnum.Select);

            List<string> result = new();
            if (isAttributeSelect)
            {
                result = SetAuthorizedValues(attributes);
            }

            if (isAttributeSelect && attributeMin != null && attributeMax != null)
            {
                result = Enumerable.Range(Convert.ToInt32(attributeMin.AttributeValue), Convert.ToInt32(attributeMax.AttributeValue))
                                   .Select(n => n.ToString())
                                   .ToList();
            }

            return result.Select(l => new SelectLookup()
            {
                Libelle = l,
                Value = l
            }).ToList();
        }

        private static List<string> SetAuthorizedValues(List<AttributeModel> attributes)
        {
            AttributeModel? attribute = attributes.FirstOrDefault(at => at.AttributeType == TSTAttributeEnum.Valeur);

            if (attribute == null)
                return new();

            return attribute.AttributeValue.Split(',').ToList();
        }

        public static int? SetIntegerAttribute(List<AttributeModel> attributes, TSTAttributeEnum attributeType)
        {
            AttributeModel? attribute = attributes.FirstOrDefault(at => at.AttributeType == attributeType);

            if (attribute is not null && !string.IsNullOrWhiteSpace(attribute.AttributeValue))
                return Convert.ToInt32(attribute.AttributeValue);

            return null;
        }
    }
}
