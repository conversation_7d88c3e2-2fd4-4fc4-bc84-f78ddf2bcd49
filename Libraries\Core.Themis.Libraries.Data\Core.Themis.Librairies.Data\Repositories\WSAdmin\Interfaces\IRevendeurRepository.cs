using Core.Themis.Libraries.Data.Entities.WSAdmin;
using Core.Themis.Libraries.Data.Repositories.Common.Interfaces;

namespace Core.Themis.Libraries.Data.Repositories.WSAdmin.Interfaces
{
    public interface IRevendeurRepository : IGenericRepository<RevendeurEntity>
    {
        /// <summary>
        /// Récupère tous les revendeurs
        /// </summary>
        /// <returns>Liste des revendeurs</returns>
        Task<IEnumerable<RevendeurEntity>> GetAllRevendeursAsync();
    }
}
