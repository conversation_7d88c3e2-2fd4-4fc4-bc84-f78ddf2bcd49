﻿using Core.Themis.Libraries.DTO.Transactionnals;
using System;
using System.Collections.Generic;

namespace Core.Themis.Libraries.DTO
{
    public class BuyerProfilDTO
    {

        public int Id { get; set; }

        public string Name { get; set; }

        public string? Nom { get; set; }

        //        public string  Surname { get; set; }

        public string Libelle { get; set; }

        public string Password { get; set; }

        public int OperatorId { get; set; }

        public int OperatorPaymentId { get; set; }

        /// <summary>
        /// identite à facturer
        /// </summary>
        public int IdentityPaiement { get; set; }


        public bool ConsumerNeeded { get; set; }
        public bool IsReseller { get; set; } = false;

        public bool IsExclusiveMode { get; set; } = false;
        public bool IsInsuranceActive { get; set; } = true;

        public DateTime CreationDate { get; set; } = DateTime.Now;

        // Propriétés additionnelles pour l'affichage des revendeurs avec leur structure
        public int? StructureId { get; set; }
        public string? StructureName { get; set; }

        public List<PaymentMethodDTO> ListPaymentMethods { get; set; } = new List<PaymentMethodDTO>();

    }
}
