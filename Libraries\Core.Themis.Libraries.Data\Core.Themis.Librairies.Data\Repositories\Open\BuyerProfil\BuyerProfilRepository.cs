﻿using AutoMapper;
using Core.Themis.Libraries.Data.DBContext;
using Core.Themis.Libraries.Data.DBContext.Interfaces;
using Core.Themis.Libraries.Data.Entities.Open.BuyerProfil;
using Core.Themis.Libraries.Data.Repositories.Common;
using Core.Themis.Libraries.Data.Repositories.Open.BuyerProfil.Interfaces;
using Core.Themis.Libraries.DTO;
using Core.Themis.Libraries.DTO.Transactionnals;
using Core.Themis.Libraries.Utilities.Helpers.Sql;
using Dapper;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;

namespace Core.Themis.Libraries.Data.Repositories.Open.BuyerProfil
{
    public class BuyerProfilRepository : GenericStructureRepository<ProfilAcheteurEntity>, IBuyerProfilRepository
    {

        private readonly IDbContext _dbContext;
        private readonly IMapper _mapper;
        private readonly string _pathForSqlScript;

        public BuyerProfilRepository(
            IDbContext dbContext, 
            IMapper mapper,
            IMemoryCache memoryCache, 
            IConfiguration config)
            : base(ContextType.Open, dbContext, memoryCache, mapper)
        {
            _dbContext = dbContext;
            _pathForSqlScript = config["PathForSqlScript"]!;
            _mapper = mapper;
        }



        public BuyerProfilDTO Get(int structureId, int buyerProfilId, string login = null, string password = null)
        {

            BuyerProfilDTO bp = new();
            try
            {
                using SqlConnection cnxOpen = _dbContext.GetStructureConnection($"{structureId:0000}");
                cnxOpen.Open();


                bool byLoginPassw = false;


                string sql = SqlQueryHelper.GetQuery(_pathForSqlScript, "BuyerProfil", "getBuyerProfilById", structureId);
                // string sql = FilesForSqlRequestsManager.GetScript(structureId, "BuyerProfil\\", "getBuyerProfilById", scriptPathSqlCommons);
                if (login != null && password != null)
                {
                    byLoginPassw = true;
                    sql = SqlQueryHelper.GetQuery(_pathForSqlScript, "BuyerProfil", "getBuyerProfilByLoginPassw", structureId);
                    //   sql = FilesForSqlRequestsManager.GetScript(structureId, "BuyerProfil\\", "getBuyerProfilByLoginPassw", scriptPathSqlCommons);
                }

                using (SqlCommand cmd = new SqlCommand())
                {
                    cmd.CommandText = sql;
                    cmd.Parameters.Add(new SqlParameter("@pid", buyerProfilId));

                    if (byLoginPassw)
                    {
                        cmd.Parameters.Add(new SqlParameter("@login", login));
                        cmd.Parameters.Add(new SqlParameter("@passw", password));
                    }
                    cmd.Connection = cnxOpen;
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        if (reader.HasRows)
                        {
                            while (reader.Read())
                            {
                                bp = new BuyerProfilDTO()
                                {
                                    Id = int.Parse(reader["id"].ToString()),
                                    Name = reader["nom"].ToString(),
                                    Libelle = reader["libelle"].ToString(),
                                    CreationDate = (DateTime)reader["date_creation"],
                                    OperatorId = int.Parse(reader["operateur_id"].ToString()),
                                    OperatorPaymentId = int.Parse(reader["paiement_operateur_id"].ToString()),
                                    IdentityPaiement = int.Parse(reader["paiement_profil_acheteur"].ToString()),
                                    ConsumerNeeded = (int)reader["consumer_needed"] == 1 ? true : false,
                                    IsReseller = (bool)reader["is_revendeur"]


                                };
                            }
                        }
                    }
                }

                sql = "SELECT * FROM mode_paiement_of_profil_acheteur mppa WHERE profil_acheteur_id =@pbpId";
                using (SqlCommand cmd = new SqlCommand())
                {
                    cmd.CommandText = sql;
                    cmd.Parameters.Add(new SqlParameter("@pbpId", bp.Id));
                    cmd.Connection = cnxOpen;
                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {

                        if (reader.HasRows)
                        {
                            while (reader.Read())
                            {
                                PaymentMethodDTO mpe = new()
                                {
                                    Id = int.Parse(reader["mode_paie_id"].ToString()),
                                };
                                bp.ListPaymentMethods.Add(mpe);
                            }
                        }

                    }
                }

            }
            catch (Exception ex)
            {
                throw;
            }



            return bp;

        }
        /// <summary>
        /// profil_acheteur
        /// </summary>
        /// <param name="structureId"></param>
        /// <returns></returns>
        public IEnumerable<ProfilAcheteurEntity> GetBuyerProfils(int structureId)
        {
            try
            {
                using SqlConnection connection = _dbContext.GetStructureConnection($"{structureId:0000}");
                connection.Open();

                string sqlQuery = @"SELECT * FROM profil_acheteur";

                return connection.Query<ProfilAcheteurEntity>(sqlQuery);
            }
            catch (System.Data.SqlClient.SqlException)
            {
                // Retourner une liste vide si la connexion à la base de données échoue
                // Cela peut arriver si la structure n'a pas de base de données configurée
                return new List<ProfilAcheteurEntity>();
            }
        }

        /// <summary>
        /// Récupère tous les revendeurs (is_revendeur = 1) d'une structure spécifique
        /// </summary>
        /// <param name="structureId"></param>
        /// <returns></returns>
        public IEnumerable<ProfilAcheteurEntity> GetRevendeurs(int structureId)
        {
            try
            {
                using SqlConnection connection = _dbContext.GetStructureConnection($"{structureId:0000}");
                connection.Open();

                string sqlQuery = @"SELECT * FROM profil_acheteur WHERE is_revendeur = 1";

                return connection.Query<ProfilAcheteurEntity>(sqlQuery);
            }
            catch (System.Data.SqlClient.SqlException)
            {
                // Retourner une liste vide si la connexion à la base de données échoue
                // Cela peut arriver si la structure n'a pas de base de données configurée
                return new List<ProfilAcheteurEntity>();
            }
        }

        /// <summary>
        /// Récupère tous les revendeurs depuis la table "revendeurs"
        /// </summary>
        /// <param name="structureId">Structure pour la connexion</param>
        /// <returns></returns>
        public IEnumerable<dynamic> GetRevendeursFromTable(int structureId)
        {
            try
            {
                using SqlConnection connection = _dbContext.GetStructureConnection($"{structureId:0000}");
                connection.Open();

                string sqlQuery = @"SELECT id, name FROM revendeurs ORDER BY name";

                return connection.Query<dynamic>(sqlQuery);
            }
            catch (System.Data.SqlClient.SqlException)
            {
                // Retourner une liste vide si la connexion à la base de données échoue
                return new List<dynamic>();
            }
        }

        /// <summary>
        /// mode_paiement_of_profil_acheteur
        /// </summary>
        /// <param name="structureId"></param>
        /// <returns></returns>
        public IEnumerable<ModePaiementOfProfilAcheteur> GetPaymentMethodOfBuyerProfils(int structureId)
        {
            using SqlConnection connection = _dbContext.GetStructureConnection($"{structureId:0000}");
            connection.Open();

            string sqlQuery = @"SELECT * FROM mode_paiement_of_profil_acheteur";

            return connection.Query<ModePaiementOfProfilAcheteur>(sqlQuery);
        }


    }
}
